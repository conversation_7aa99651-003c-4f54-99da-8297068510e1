#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双色球下期号码预测脚本
基于历史数据的统计学分析和机器学习方法
"""

from lottery_predictor import LotteryPredictor
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>

def predict_next_period():
    """预测下一期双色球号码"""
    
    print("=" * 60)
    print("🎯 双色球智能预测系统 🎯")
    print("=" * 60)
    
    # 创建预测器
    predictor = LotteryPredictor()
    
    # 加载数据
    print("📊 正在加载历史数据...")
    if not predictor.load_data():
        print("❌ 数据加载失败！")
        return
    
    print("✅ 数据加载成功！")
    print(f"📈 历史数据总期数: {len(predictor.ssqhistory_all)}")
    
    # 分析数据
    print("\n🔍 正在分析历史数据...")
    predictor.analyze_data()
    predictor.calculate_intervals()
    
    # 获取最新期号信息
    latest_row = predictor.ssqhistory_all.iloc[-1]
    latest_period = int(latest_row['期号'])
    latest_red = [int(latest_row[f'红球{i}']) for i in range(1, 7)]
    latest_blue = int(latest_row['蓝球'])
    
    print(f"\n📅 最新开奖期号: {latest_period}")
    print(f"🔴 最新红球号码: {latest_red}")
    print(f"🔵 最新蓝球号码: {latest_blue}")
    
    # 预测下一期
    next_period = latest_period + 1
    current_period_index = len(predictor.ssqhistory_all)
    
    print(f"\n🎲 正在预测第 {next_period} 期号码...")
    
    # 使用多种方法预测
    methods = {
        '统计学方法': predictor.predict_numbers_statistical,
        '机器学习方法': predictor.predict_numbers_ml
    }
    
    predictions = {}
    
    for method_name, method_func in methods.items():
        print(f"   🔄 {method_name}预测中...")
        red_pred, blue_pred = method_func(current_period_index)
        predictions[method_name] = {
            'red': red_pred,
            'blue': blue_pred
        }
    
    # 显示预测结果
    print("\n" + "=" * 60)
    print("🎯 预测结果")
    print("=" * 60)
    
    for method_name, pred in predictions.items():
        print(f"\n📊 {method_name}:")
        print(f"   🔴 红球: {pred['red']}")
        print(f"   🔵 蓝球: {pred['blue']}")
    
    # 综合预测（投票机制）
    print(f"\n🤖 综合预测 (投票机制):")
    
    # 红球综合预测
    all_red_predictions = []
    for pred in predictions.values():
        all_red_predictions.extend(pred['red'])
    
    # 统计每个红球号码的出现次数
    red_votes = {}
    for ball in all_red_predictions:
        red_votes[ball] = red_votes.get(ball, 0) + 1
    
    # 选择得票最多的6个红球
    sorted_red = sorted(red_votes.items(), key=lambda x: x[1], reverse=True)
    consensus_red = [ball for ball, votes in sorted_red[:6]]
    
    # 如果不足6个，随机补充
    if len(consensus_red) < 6:
        available = [i for i in range(1, 34) if i not in consensus_red]
        needed = 6 - len(consensus_red)
        consensus_red.extend(np.random.choice(available, needed, replace=False))
    
    consensus_red = sorted(consensus_red)
    
    # 蓝球综合预测（简单平均）
    blue_predictions = [pred['blue'] for pred in predictions.values()]
    consensus_blue = int(round(np.mean(blue_predictions)))
    consensus_blue = max(1, min(16, consensus_blue))
    
    print(f"   🔴 红球: {consensus_red}")
    print(f"   🔵 蓝球: {consensus_blue}")
    
    # 显示推荐理由
    print("\n" + "=" * 60)
    print("💡 预测分析")
    print("=" * 60)
    
    # 分析红球选择理由
    print("\n🔴 红球分析:")
    red_freq = predictor.red_ball_stats.get('frequency', {})
    red_intervals = predictor.red_ball_stats.get('intervals', {})
    
    for ball in consensus_red:
        freq = red_freq.get(ball, 0)
        avg_interval = red_intervals.get(ball, {}).get('avg_interval', 0)
        print(f"   {ball:2d}: 历史出现{freq}次, 平均间隔{avg_interval:.1f}期")
    
    # 分析蓝球选择理由
    print(f"\n🔵 蓝球分析:")
    blue_freq = predictor.blue_ball_stats.get('frequency', {})
    blue_count = blue_freq.get(consensus_blue, 0)
    print(f"   {consensus_blue:2d}: 历史出现{blue_count}次")
    
    # 风险提示
    print("\n" + "=" * 60)
    print("⚠️  风险提示")
    print("=" * 60)
    print("• 彩票具有随机性，任何预测方法都无法保证中奖")
    print("• 本系统仅供参考，请理性购彩")
    print("• 根据回归测试，预测准确率有限")
    print("• 建议小额投注，切勿沉迷")
    
    # 保存预测结果
    save_prediction(next_period, consensus_red, consensus_blue, predictions)
    
    return {
        'period': next_period,
        'consensus_red': consensus_red,
        'consensus_blue': consensus_blue,
        'method_predictions': predictions
    }

def save_prediction(period, red_balls, blue_ball, method_predictions):
    """保存预测结果到文件"""
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 创建预测记录
    prediction_record = {
        'timestamp': timestamp,
        'period': period,
        'consensus_red': red_balls,
        'consensus_blue': blue_ball,
        'method_predictions': method_predictions
    }
    
    # 保存到CSV文件
    try:
        # 尝试读取现有文件
        try:
            df = pd.read_csv('predictions.csv')
        except FileNotFoundError:
            df = pd.DataFrame()
        
        # 添加新预测
        new_row = {
            'timestamp': timestamp,
            'period': period,
            'red_balls': ','.join(map(str, red_balls)),
            'blue_ball': blue_ball
        }
        
        # 添加各方法的预测
        for method, pred in method_predictions.items():
            new_row[f'{method}_red'] = ','.join(map(str, pred['red']))
            new_row[f'{method}_blue'] = pred['blue']
        
        df = pd.concat([df, pd.DataFrame([new_row])], ignore_index=True)
        df.to_csv('predictions.csv', index=False)
        
        print(f"\n💾 预测结果已保存到 predictions.csv")
        
    except Exception as e:
        print(f"\n❌ 保存预测结果失败: {e}")

def show_recent_accuracy():
    """显示最近的预测准确性"""
    try:
        df = pd.read_csv('predictions.csv')
        if len(df) > 0:
            print(f"\n📊 历史预测记录: {len(df)} 条")
            print("最近5次预测:")
            recent = df.tail(5)
            for _, row in recent.iterrows():
                print(f"   期号{row['period']}: {row['red_balls']} + {row['blue_ball']}")
    except FileNotFoundError:
        print("\n📊 暂无历史预测记录")

if __name__ == "__main__":
    # 显示历史预测
    show_recent_accuracy()
    
    # 进行新预测
    result = predict_next_period()
    
    if result:
        print(f"\n🎉 预测完成！祝您好运！ 🍀")
