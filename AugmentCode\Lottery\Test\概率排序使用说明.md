# 概率排序预测方法 - 详细使用说明

## 🎯 概率排序方法简介

概率排序方法是基于您的原创思路开发的预测算法：
1. 统计某期之前N期的红蓝球出现概率
2. 将当期实际开奖号码按概率排序
3. 寻找稳定的排序组合模式
4. 使用发现的模式预测下期号码

## 📁 核心文件说明

### 主要程序文件
- `probability_ranking_predictor.py` - 概率排序预测器（核心文件）
- `ranking_pattern_analyzer.py` - 排序模式深度分析器
- `lottery_predictor.py` - 基础预测器（依赖文件）

### 数据文件
- `lottery_data_all.xlsx` - 历史开奖数据（必需）

### 输出文件
- `ranking_prediction_results.txt` - 概率排序预测结果
- `pattern_analysis_results.txt` - 模式分析详细结果
- `ranking_analysis.png` - 排序分析可视化图表

## 🚀 快速开始指南

### 方法一：快速预测（最简单，推荐新手）

```bash
python quick_ranking_predict.py
```

**这是最简单的使用方式，会直接给出预测结果：**
- 自动使用最优参数（100期回看，最佳排序模式）
- 显示详细的概率分析
- 保存结果到 quick_prediction.txt
- 包含方法说明和风险提示

### 方法二：完整分析（推荐进阶用户）

```bash
python probability_ranking_predictor.py
```

**这个命令会自动完成：**
1. 寻找最佳回看期数（50-200期测试）
2. 分析排序模式
3. 生成可视化图表
4. 进行回归测试
5. 预测下一期号码

**预期输出：**
```
🎯 概率排序预测系统
基于历史概率统计的排序选号方法
================================================================================

🔍 第一步：寻找最佳回看期数...
✅ 最佳回看期数: 75 (稳定性得分: 0.xxxx)

📊 第二步：分析排序模式...
📈 第三步：生成可视化分析...
✅ 排序分析图表已保存为 ranking_analysis.png

🧪 第四步：全面回归测试...
🏆 回归测试结果排名
================================================================================
高概率模式           1.11         6.8%       1.24
跳跃模式            1.09         7.1%       1.23
...

🎯 第五步：预测下一期...
🎊 第25088期概率排序预测:
🔴 红球: [2, 6, 10, 14, 30, 33]
🔵 蓝球: 11
```

### 方法二：深度模式分析（高级用户）

```bash
python ranking_pattern_analyzer.py
```

**这个命令会：**
1. 深度分析排序模式的稳定性
2. 寻找最优排序策略
3. 生成最终预测

## 📊 详细使用步骤

### 第一步：环境准备

确保安装了必要的Python包：
```bash
pip install pandas numpy matplotlib seaborn scipy scikit-learn openpyxl
```

### 第二步：数据准备

确保 `lottery_data_all.xlsx` 文件在程序目录中，文件应包含：
- `SSQ_data_all` 工作表
- A列：期号
- I-O列：红球1-6，蓝球

### 第三步：运行预测

#### 选项A：完整分析（推荐新手）
```bash
python probability_ranking_predictor.py
```

#### 选项B：快速预测（有经验用户）
```python
from probability_ranking_predictor import ProbabilityRankingPredictor
from lottery_predictor import LotteryPredictor

# 初始化
base_predictor = LotteryPredictor()
base_predictor.load_data()
base_predictor.analyze_data()

prob_predictor = ProbabilityRankingPredictor(base_predictor)

# 使用最佳模式预测
best_pattern = {
    'red_rankings': [2, 11, 14, 17, 21, 28],  # 最优红球排序位置
    'blue_ranking': 8  # 最优蓝球排序位置
}

current_period_index = len(base_predictor.ssqhistory_all)
red_pred, blue_pred = prob_predictor.predict_by_ranking(
    current_period_index, 100, best_pattern)

print(f"预测红球: {red_pred}")
print(f"预测蓝球: {blue_pred}")
```

## 🔧 高级配置选项

### 自定义回看期数
```python
# 测试不同的回看期数
optimal_results = prob_predictor.find_optimal_lookback_period(
    lookback_range=(50, 150, 25)  # 起始期数, 结束期数, 步长
)
```

### 自定义排序模式
```python
# 定义自己的排序模式
custom_pattern = {
    'red_rankings': [1, 5, 10, 15, 20, 25],  # 红球排序位置
    'blue_ranking': 3  # 蓝球排序位置
}

# 使用自定义模式预测
red_pred, blue_pred = prob_predictor.predict_by_ranking(
    period_index, 100, custom_pattern)
```

### 回归测试特定模式
```python
# 测试特定模式的性能
backtest_results = prob_predictor.comprehensive_backtest(
    start_period=500,
    end_period=None,
    lookback_periods=100,
    test_patterns={'我的模式': custom_pattern}
)
```

## 📈 结果解读

### 预测结果格式
```
🎊 第25088期概率排序预测:
🔴 红球: [2, 6, 10, 14, 30, 33]
🔵 蓝球: 11
📊 使用模式: 高概率模式
🔍 回看期数: 75
```

### 性能指标说明
- **平均红球命中**: 平均每期命中的红球个数
- **蓝球命中率**: 蓝球预测准确的百分比
- **平均总分**: 红球命中数 + 蓝球命中数×2
- **稳定性得分**: 0-1之间，越高越稳定

### 排序位置含义
- **排序位置1**: 概率最高的号码
- **排序位置17**: 概率中等的号码
- **排序位置33**: 概率最低的号码

## 🎯 最佳实践建议

### 1. 使用经过验证的最优参数
```python
# 基于测试结果的最优配置
OPTIMAL_LOOKBACK = 100  # 最佳回看期数
BEST_PATTERN = {
    'red_rankings': [2, 11, 14, 17, 21, 28],
    'blue_ranking': 8
}
```

### 2. 定期更新数据
- 建议每期开奖后更新Excel数据文件
- 重新运行分析以获得最新的预测结果

### 3. 结合多种方法
```python
# 运行综合预测系统
python comprehensive_final_predictor.py
```

### 4. 关注稳定性指标
- 选择稳定性得分高的排序模式
- 避免使用出现频率过低的模式

## 🔍 故障排除

### 常见问题及解决方案

#### 问题1：数据加载失败
```
❌ 数据加载失败！
```
**解决方案：**
- 检查 `lottery_data_all.xlsx` 文件是否存在
- 确认文件中有 `SSQ_data_all` 工作表
- 验证数据格式是否正确

#### 问题2：预测结果异常
```
预测红球: [np.int64(2), np.int64(6), ...]
```
**解决方案：**
- 这是正常的，np.int64会自动转换为整数
- 或者手动转换：`[int(x) for x in red_pred]`

#### 问题3：内存不足
**解决方案：**
- 减少测试期数：`test_periods=200`
- 减少回看期数范围：`lookback_range=(50, 100, 25)`

## 📋 完整示例代码

```python
#!/usr/bin/env python3
# 概率排序预测完整示例

from probability_ranking_predictor import ProbabilityRankingPredictor
from lottery_predictor import LotteryPredictor

def simple_predict():
    """简单预测示例"""
    
    # 1. 初始化
    base_predictor = LotteryPredictor()
    if not base_predictor.load_data():
        print("数据加载失败！")
        return
    
    base_predictor.analyze_data()
    prob_predictor = ProbabilityRankingPredictor(base_predictor)
    
    # 2. 使用最优模式预测
    best_pattern = {
        'red_rankings': [2, 11, 14, 17, 21, 28],
        'blue_ranking': 8
    }
    
    current_period_index = len(base_predictor.ssqhistory_all)
    red_pred, blue_pred = prob_predictor.predict_by_ranking(
        current_period_index, 100, best_pattern)
    
    # 3. 显示结果
    latest_period = int(base_predictor.ssqhistory_all.iloc[-1]['期号'])
    next_period = latest_period + 1
    
    print(f"第{next_period}期概率排序预测:")
    print(f"红球: {[int(x) for x in red_pred]}")
    print(f"蓝球: {int(blue_pred)}")

if __name__ == "__main__":
    simple_predict()
```

## ⚠️ 重要提醒

1. **理性购彩**: 概率排序方法虽然在统计上有所提升，但彩票仍具有随机性
2. **仅供参考**: 预测结果仅供参考，不保证中奖
3. **适量投注**: 请根据个人经济能力适量投注
4. **学习目的**: 建议将此方法作为学习统计学和数据分析的工具

## 📞 技术支持

如果在使用过程中遇到问题，可以：
1. 检查错误信息和日志输出
2. 确认数据文件格式正确
3. 验证Python环境和依赖包
4. 参考输出的详细结果文件

---

**祝您使用愉快！记住理性购彩，量力而行！** 🍀
