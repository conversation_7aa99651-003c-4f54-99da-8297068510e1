#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
概率排序预测器
基于历史N期概率统计的排序选号方法
"""

import numpy as np
import pandas as pd
from lottery_predictor import LotteryPredictor
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ProbabilityRankingPredictor:
    """概率排序预测器"""
    
    def __init__(self, base_predictor):
        """
        初始化概率排序预测器
        
        Args:
            base_predictor: 基础预测器实例
        """
        self.base_predictor = base_predictor
        self.data = base_predictor.ssqhistory_all
        self.ranking_patterns = {}
        self.optimal_lookback = 100  # 默认回看期数
        
    def calculate_probability_table(self, period_index, lookback_periods):
        """
        计算指定期数前N期的红蓝球概率表
        
        Args:
            period_index: 当前期数索引
            lookback_periods: 回看期数
        
        Returns:
            red_probs, blue_probs: 红球和蓝球概率表
        """
        start_idx = max(0, period_index - lookback_periods)
        history_data = self.data.iloc[start_idx:period_index]
        
        # 红球概率统计
        red_counts = np.zeros(33)
        total_red_draws = len(history_data) * 6
        
        for _, row in history_data.iterrows():
            for i in range(1, 7):
                ball = int(row[f'红球{i}'])
                red_counts[ball-1] += 1
        
        red_probs = red_counts / total_red_draws if total_red_draws > 0 else np.ones(33) / 33
        
        # 蓝球概率统计
        blue_counts = np.zeros(16)
        total_blue_draws = len(history_data)
        
        for _, row in history_data.iterrows():
            ball = int(row['蓝球'])
            blue_counts[ball-1] += 1
        
        blue_probs = blue_counts / total_blue_draws if total_blue_draws > 0 else np.ones(16) / 16
        
        return red_probs, blue_probs
    
    def get_actual_ranking(self, period_index, red_probs, blue_probs):
        """
        获取实际开奖号码在概率表中的排序位置
        
        Args:
            period_index: 期数索引
            red_probs: 红球概率表
            blue_probs: 蓝球概率表
        
        Returns:
            red_rankings, blue_ranking: 红球和蓝球的排序位置
        """
        actual_row = self.data.iloc[period_index]
        actual_red = [int(actual_row[f'红球{i}']) for i in range(1, 7)]
        actual_blue = int(actual_row['蓝球'])
        
        # 红球排序（按概率从高到低）
        red_sorted_indices = np.argsort(red_probs)[::-1]  # 降序排列
        red_rankings = []
        
        for ball in actual_red:
            ranking = np.where(red_sorted_indices == ball-1)[0][0] + 1  # 排序位置（1-based）
            red_rankings.append(ranking)
        
        # 蓝球排序
        blue_sorted_indices = np.argsort(blue_probs)[::-1]
        blue_ranking = np.where(blue_sorted_indices == actual_blue-1)[0][0] + 1
        
        return sorted(red_rankings), blue_ranking
    
    def analyze_ranking_patterns(self, start_period=200, end_period=None, lookback_periods=100):
        """
        分析排序模式
        
        Args:
            start_period: 开始分析的期数
            end_period: 结束分析的期数
            lookback_periods: 回看期数
        
        Returns:
            pattern_analysis: 排序模式分析结果
        """
        if end_period is None:
            end_period = len(self.data) - 1
        
        print(f"🔍 分析排序模式 (回看{lookback_periods}期)...")
        print(f"📊 分析范围: 第{start_period}期 到 第{end_period}期")
        
        all_red_rankings = []
        all_blue_rankings = []
        ranking_combinations = defaultdict(int)
        
        for period_idx in range(start_period, end_period + 1):
            if period_idx % 100 == 0:
                print(f"   进度: {period_idx - start_period + 1}/{end_period - start_period + 1}")
            
            # 计算概率表
            red_probs, blue_probs = self.calculate_probability_table(period_idx, lookback_periods)
            
            # 获取实际排序
            red_rankings, blue_ranking = self.get_actual_ranking(period_idx, red_probs, blue_probs)
            
            all_red_rankings.append(red_rankings)
            all_blue_rankings.append(blue_ranking)
            
            # 记录排序组合
            ranking_key = tuple(red_rankings + [blue_ranking])
            ranking_combinations[ranking_key] += 1
        
        # 分析结果
        pattern_analysis = {
            'total_samples': len(all_red_rankings),
            'red_ranking_stats': self._analyze_red_rankings(all_red_rankings),
            'blue_ranking_stats': self._analyze_blue_rankings(all_blue_rankings),
            'common_combinations': self._find_common_combinations(ranking_combinations),
            'lookback_periods': lookback_periods
        }
        
        return pattern_analysis
    
    def _analyze_red_rankings(self, all_red_rankings):
        """分析红球排序统计"""
        # 统计每个位置的排序分布
        position_stats = {}
        
        for pos in range(6):  # 6个红球位置
            position_rankings = [rankings[pos] for rankings in all_red_rankings]
            position_stats[f'位置{pos+1}'] = {
                'mean': np.mean(position_rankings),
                'std': np.std(position_rankings),
                'median': np.median(position_rankings),
                'min': min(position_rankings),
                'max': max(position_rankings),
                'distribution': np.bincount(position_rankings, minlength=34)[1:34]  # 1-33的分布
            }
        
        # 整体统计
        all_rankings_flat = [rank for rankings in all_red_rankings for rank in rankings]
        overall_stats = {
            'mean': np.mean(all_rankings_flat),
            'std': np.std(all_rankings_flat),
            'distribution': np.bincount(all_rankings_flat, minlength=34)[1:34]
        }
        
        return {
            'position_stats': position_stats,
            'overall_stats': overall_stats
        }
    
    def _analyze_blue_rankings(self, all_blue_rankings):
        """分析蓝球排序统计"""
        return {
            'mean': np.mean(all_blue_rankings),
            'std': np.std(all_blue_rankings),
            'median': np.median(all_blue_rankings),
            'min': min(all_blue_rankings),
            'max': max(all_blue_rankings),
            'distribution': np.bincount(all_blue_rankings, minlength=17)[1:17]  # 1-16的分布
        }
    
    def _find_common_combinations(self, ranking_combinations):
        """找出常见的排序组合"""
        # 按出现次数排序
        sorted_combinations = sorted(ranking_combinations.items(), 
                                   key=lambda x: x[1], reverse=True)
        
        total_samples = sum(ranking_combinations.values())
        
        common_combinations = []
        for combination, count in sorted_combinations[:20]:  # 取前20个
            percentage = count / total_samples * 100
            red_rankings = list(combination[:6])
            blue_ranking = combination[6]
            
            common_combinations.append({
                'red_rankings': red_rankings,
                'blue_ranking': blue_ranking,
                'count': count,
                'percentage': percentage
            })
        
        return common_combinations
    
    def find_optimal_lookback_period(self, test_periods=None, lookback_range=(50, 200, 10)):
        """
        寻找最佳回看期数
        
        Args:
            test_periods: 测试期数范围
            lookback_range: 回看期数范围 (start, end, step)
        
        Returns:
            optimal_results: 最佳参数结果
        """
        if test_periods is None:
            total_periods = len(self.data)
            test_periods = (total_periods - 500, total_periods - 1)  # 测试最近500期
        
        start_period, end_period = test_periods
        lookback_start, lookback_end, lookback_step = lookback_range
        
        print(f"🔍 寻找最佳回看期数...")
        print(f"📊 测试范围: {lookback_start}-{lookback_end}期 (步长{lookback_step})")
        print(f"📈 验证期数: 第{start_period}期 到 第{end_period}期")
        
        results = {}
        
        for lookback in range(lookback_start, lookback_end + 1, lookback_step):
            print(f"   测试回看期数: {lookback}")
            
            # 分析该回看期数的模式
            pattern_analysis = self.analyze_ranking_patterns(
                start_period, end_period, lookback)
            
            # 评估模式稳定性
            stability_score = self._evaluate_pattern_stability(pattern_analysis)
            
            results[lookback] = {
                'pattern_analysis': pattern_analysis,
                'stability_score': stability_score
            }
        
        # 找出最佳回看期数
        best_lookback = max(results.keys(), key=lambda k: results[k]['stability_score'])
        
        optimal_results = {
            'best_lookback': best_lookback,
            'best_score': results[best_lookback]['stability_score'],
            'all_results': results
        }
        
        print(f"✅ 最佳回看期数: {best_lookback} (稳定性得分: {results[best_lookback]['stability_score']:.4f})")
        
        return optimal_results
    
    def _evaluate_pattern_stability(self, pattern_analysis):
        """评估模式稳定性"""
        # 基于多个指标评估稳定性
        
        # 1. 红球排序分布的均匀性
        red_overall = pattern_analysis['red_ranking_stats']['overall_stats']
        red_distribution = red_overall['distribution']
        red_uniformity = 1 - np.std(red_distribution) / np.mean(red_distribution) if np.mean(red_distribution) > 0 else 0
        
        # 2. 蓝球排序分布的均匀性
        blue_stats = pattern_analysis['blue_ranking_stats']
        blue_distribution = blue_stats['distribution']
        blue_uniformity = 1 - np.std(blue_distribution) / np.mean(blue_distribution) if np.mean(blue_distribution) > 0 else 0
        
        # 3. 常见组合的集中度
        common_combinations = pattern_analysis['common_combinations']
        if common_combinations:
            top_combination_percentage = common_combinations[0]['percentage']
            concentration = min(top_combination_percentage / 10, 1)  # 归一化到0-1
        else:
            concentration = 0
        
        # 综合稳定性得分
        stability_score = (red_uniformity * 0.4 + blue_uniformity * 0.3 + concentration * 0.3)
        
        return stability_score
    
    def predict_by_ranking(self, period_index, lookback_periods=None, ranking_pattern=None):
        """
        基于排序模式预测
        
        Args:
            period_index: 当前期数索引
            lookback_periods: 回看期数
            ranking_pattern: 指定的排序模式
        
        Returns:
            predicted_red, predicted_blue: 预测结果
        """
        if lookback_periods is None:
            lookback_periods = self.optimal_lookback
        
        # 计算概率表
        red_probs, blue_probs = self.calculate_probability_table(period_index, lookback_periods)
        
        # 按概率排序
        red_sorted_indices = np.argsort(red_probs)[::-1]  # 降序
        blue_sorted_indices = np.argsort(blue_probs)[::-1]
        
        if ranking_pattern is None:
            # 使用默认排序模式（基于历史分析的最常见模式）
            ranking_pattern = self._get_default_ranking_pattern()
        
        # 根据排序模式选择号码
        predicted_red = []
        for rank in ranking_pattern['red_rankings']:
            if rank <= 33:
                ball = red_sorted_indices[rank-1] + 1  # 转换为球号
                predicted_red.append(ball)
        
        # 确保有6个红球
        while len(predicted_red) < 6:
            for i in range(33):
                ball = red_sorted_indices[i] + 1
                if ball not in predicted_red:
                    predicted_red.append(ball)
                    break
        
        predicted_red = sorted(predicted_red[:6])
        
        # 蓝球预测
        blue_rank = ranking_pattern['blue_ranking']
        if blue_rank <= 16:
            predicted_blue = blue_sorted_indices[blue_rank-1] + 1
        else:
            predicted_blue = blue_sorted_indices[0] + 1  # 默认选择概率最高的
        
        return predicted_red, predicted_blue
    
    def _get_default_ranking_pattern(self):
        """获取默认排序模式"""
        # 基于经验的默认模式：选择中等概率的号码
        return {
            'red_rankings': [3, 7, 11, 15, 19, 23],  # 中等排序位置
            'blue_ranking': 5  # 中等排序位置
        }

    def comprehensive_backtest(self, start_period=300, end_period=None,
                             lookback_periods=None, test_patterns=None):
        """
        全面回归测试

        Args:
            start_period: 开始测试期数
            end_period: 结束测试期数
            lookback_periods: 回看期数
            test_patterns: 测试的排序模式列表

        Returns:
            backtest_results: 回归测试结果
        """
        if end_period is None:
            end_period = len(self.data) - 1

        if lookback_periods is None:
            lookback_periods = self.optimal_lookback

        if test_patterns is None:
            test_patterns = self._generate_test_patterns()

        print(f"🧪 开始全面回归测试...")
        print(f"📊 测试期数: 第{start_period}期 到 第{end_period}期")
        print(f"🔍 回看期数: {lookback_periods}")
        print(f"📋 测试模式数: {len(test_patterns)}")

        results = {}

        for pattern_name, pattern in test_patterns.items():
            print(f"   测试模式: {pattern_name}")

            red_hits = []
            blue_hits = []
            total_scores = []

            for period_idx in range(start_period, end_period + 1):
                # 预测
                pred_red, pred_blue = self.predict_by_ranking(
                    period_idx, lookback_periods, pattern)

                # 获取实际结果
                actual_row = self.data.iloc[period_idx]
                actual_red = [int(actual_row[f'红球{i}']) for i in range(1, 7)]
                actual_blue = int(actual_row['蓝球'])

                # 计算命中
                red_hit = len(set(pred_red) & set(actual_red))
                blue_hit = 1 if pred_blue == actual_blue else 0
                total_score = red_hit + blue_hit * 2

                red_hits.append(red_hit)
                blue_hits.append(blue_hit)
                total_scores.append(total_score)

            # 统计结果
            results[pattern_name] = {
                'pattern': pattern,
                'avg_red_hits': np.mean(red_hits),
                'std_red_hits': np.std(red_hits),
                'blue_hit_rate': np.mean(blue_hits) * 100,
                'avg_total_score': np.mean(total_scores),
                'total_tests': len(red_hits),
                'red_hit_distribution': [red_hits.count(i) for i in range(7)]
            }

        # 排序结果
        sorted_results = sorted(results.items(),
                              key=lambda x: x[1]['avg_total_score'], reverse=True)

        backtest_results = {
            'sorted_results': sorted_results,
            'all_results': results,
            'test_params': {
                'start_period': start_period,
                'end_period': end_period,
                'lookback_periods': lookback_periods,
                'total_tests': end_period - start_period + 1
            }
        }

        return backtest_results

    def _generate_test_patterns(self):
        """生成测试用的排序模式"""
        patterns = {}

        # 1. 高概率模式（选择排序靠前的号码）
        patterns['高概率模式'] = {
            'red_rankings': [1, 2, 3, 4, 5, 6],
            'blue_ranking': 1
        }

        # 2. 低概率模式（选择排序靠后的号码）
        patterns['低概率模式'] = {
            'red_rankings': [28, 29, 30, 31, 32, 33],
            'blue_ranking': 16
        }

        # 3. 中等概率模式
        patterns['中等概率模式'] = {
            'red_rankings': [14, 15, 16, 17, 18, 19],
            'blue_ranking': 8
        }

        # 4. 混合模式1（高中低混合）
        patterns['混合模式1'] = {
            'red_rankings': [1, 8, 15, 22, 29, 33],
            'blue_ranking': 5
        }

        # 5. 混合模式2
        patterns['混合模式2'] = {
            'red_rankings': [3, 7, 11, 15, 19, 23],
            'blue_ranking': 6
        }

        # 6. 跳跃模式（等间隔选择）
        patterns['跳跃模式'] = {
            'red_rankings': [2, 7, 12, 17, 22, 27],
            'blue_ranking': 4
        }

        # 7. 黄金分割模式
        patterns['黄金分割模式'] = {
            'red_rankings': [5, 8, 13, 16, 21, 26],
            'blue_ranking': 7
        }

        # 8. 随机模式（作为对照）
        np.random.seed(42)
        patterns['随机模式'] = {
            'red_rankings': sorted(np.random.choice(range(1, 34), 6, replace=False)),
            'blue_ranking': np.random.choice(range(1, 17))
        }

        return patterns

    def visualize_ranking_analysis(self, pattern_analysis, save_path='ranking_analysis.png'):
        """可视化排序分析结果"""

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))

        # 1. 红球整体排序分布
        red_overall = pattern_analysis['red_ranking_stats']['overall_stats']
        red_dist = red_overall['distribution']

        axes[0, 0].bar(range(1, 34), red_dist, color='red', alpha=0.7)
        axes[0, 0].set_title('红球排序位置分布')
        axes[0, 0].set_xlabel('排序位置')
        axes[0, 0].set_ylabel('出现次数')

        # 2. 蓝球排序分布
        blue_stats = pattern_analysis['blue_ranking_stats']
        blue_dist = blue_stats['distribution']

        axes[0, 1].bar(range(1, 17), blue_dist, color='blue', alpha=0.7)
        axes[0, 1].set_title('蓝球排序位置分布')
        axes[0, 1].set_xlabel('排序位置')
        axes[0, 1].set_ylabel('出现次数')

        # 3. 红球各位置平均排序
        position_means = []
        position_stds = []
        red_position_stats = pattern_analysis['red_ranking_stats']['position_stats']

        for i in range(6):
            pos_key = f'位置{i+1}'
            position_means.append(red_position_stats[pos_key]['mean'])
            position_stds.append(red_position_stats[pos_key]['std'])

        axes[0, 2].errorbar(range(1, 7), position_means, yerr=position_stds,
                           marker='o', capsize=5, color='red')
        axes[0, 2].set_title('红球各位置平均排序')
        axes[0, 2].set_xlabel('红球位置')
        axes[0, 2].set_ylabel('平均排序位置')

        # 4. 常见排序组合
        common_combinations = pattern_analysis['common_combinations'][:10]
        combo_names = [f"组合{i+1}" for i in range(len(common_combinations))]
        combo_percentages = [combo['percentage'] for combo in common_combinations]

        axes[1, 0].bar(combo_names, combo_percentages, color='green', alpha=0.7)
        axes[1, 0].set_title('最常见排序组合')
        axes[1, 0].set_xlabel('排序组合')
        axes[1, 0].set_ylabel('出现百分比 (%)')
        axes[1, 0].tick_params(axis='x', rotation=45)

        # 5. 红球排序热力图
        position_distributions = np.zeros((6, 33))
        for i in range(6):
            pos_key = f'位置{i+1}'
            position_distributions[i] = red_position_stats[pos_key]['distribution']

        sns.heatmap(position_distributions, annot=False, cmap='Reds',
                   xticklabels=[str(i) for i in range(1, 34)], yticklabels=[f'位置{i+1}' for i in range(6)],
                   ax=axes[1, 1])
        axes[1, 1].set_title('红球位置-排序热力图')
        axes[1, 1].set_xlabel('排序位置')

        # 6. 统计摘要
        axes[1, 2].axis('off')
        summary_text = f"""
        排序分析摘要

        总样本数: {pattern_analysis['total_samples']}
        回看期数: {pattern_analysis['lookback_periods']}

        红球排序统计:
        平均排序: {red_overall['mean']:.2f}
        标准差: {red_overall['std']:.2f}

        蓝球排序统计:
        平均排序: {blue_stats['mean']:.2f}
        标准差: {blue_stats['std']:.2f}

        最常见组合:
        {common_combinations[0]['red_rankings']} + {common_combinations[0]['blue_ranking']}
        出现率: {common_combinations[0]['percentage']:.2f}%
        """

        axes[1, 2].text(0.1, 0.9, summary_text, transform=axes[1, 2].transAxes,
                        fontsize=10, verticalalignment='top', fontfamily='monospace')

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

        print(f"✅ 排序分析图表已保存为 {save_path}")

def main():
    """主函数"""
    print("=" * 80)
    print("🎯 概率排序预测系统")
    print("基于历史概率统计的排序选号方法")
    print("=" * 80)

    # 初始化
    base_predictor = LotteryPredictor()
    if not base_predictor.load_data():
        print("❌ 数据加载失败！")
        return

    base_predictor.analyze_data()

    # 创建概率排序预测器
    prob_predictor = ProbabilityRankingPredictor(base_predictor)

    # 1. 寻找最佳回看期数
    print("\n🔍 第一步：寻找最佳回看期数...")
    optimal_results = prob_predictor.find_optimal_lookback_period(
        lookback_range=(50, 200, 25))

    best_lookback = optimal_results['best_lookback']
    prob_predictor.optimal_lookback = best_lookback

    # 2. 分析排序模式
    print(f"\n📊 第二步：分析排序模式 (使用最佳回看期数: {best_lookback})...")
    pattern_analysis = prob_predictor.analyze_ranking_patterns(
        start_period=300, lookback_periods=best_lookback)

    # 3. 可视化分析
    print("\n📈 第三步：生成可视化分析...")
    prob_predictor.visualize_ranking_analysis(pattern_analysis)

    # 4. 全面回归测试
    print("\n🧪 第四步：全面回归测试...")
    backtest_results = prob_predictor.comprehensive_backtest(
        start_period=500, lookback_periods=best_lookback)

    # 显示测试结果
    print("\n" + "=" * 80)
    print("🏆 回归测试结果排名")
    print("=" * 80)

    print(f"{'模式名称':<15} {'平均红球命中':<12} {'蓝球命中率':<10} {'平均总分':<8}")
    print("-" * 60)

    for pattern_name, results in backtest_results['sorted_results']:
        print(f"{pattern_name:<15} {results['avg_red_hits']:<12.2f} "
              f"{results['blue_hit_rate']:<10.1f}% {results['avg_total_score']:<8.2f}")

    # 最佳模式详细分析
    best_pattern_name, best_results = backtest_results['sorted_results'][0]
    print(f"\n🥇 最佳排序模式: {best_pattern_name}")
    print(f"   排序组合: 红球{best_results['pattern']['red_rankings']} "
          f"蓝球{best_results['pattern']['blue_ranking']}")
    print(f"   平均红球命中: {best_results['avg_red_hits']:.2f} ± {best_results['std_red_hits']:.2f}")
    print(f"   蓝球命中率: {best_results['blue_hit_rate']:.1f}%")
    print(f"   平均总分: {best_results['avg_total_score']:.2f}")

    # 5. 预测下一期
    print(f"\n🎯 第五步：预测下一期...")
    if base_predictor.ssqhistory_all is not None:
        current_period_index = len(base_predictor.ssqhistory_all)
        latest_period = int(base_predictor.ssqhistory_all.iloc[-1]['期号'])
        next_period = latest_period + 1
    else:
        print("❌ 数据不可用，无法预测")
        return None

    # 使用最佳模式预测
    best_pattern = best_results['pattern']
    pred_red, pred_blue = prob_predictor.predict_by_ranking(
        current_period_index, best_lookback, best_pattern)

    print(f"\n🎊 第{next_period}期概率排序预测:")
    print(f"🔴 红球: {pred_red}")
    print(f"🔵 蓝球: {pred_blue}")
    print(f"📊 使用模式: {best_pattern_name}")
    print(f"🔍 回看期数: {best_lookback}")

    # 保存结果
    save_ranking_results(next_period, pred_red, pred_blue, best_pattern_name,
                        best_lookback, backtest_results, pattern_analysis)

    return {
        'next_period': next_period,
        'prediction': {'red': pred_red, 'blue': pred_blue},
        'best_pattern': best_pattern_name,
        'best_lookback': best_lookback,
        'backtest_results': backtest_results
    }

def save_ranking_results(period, red_balls, blue_ball, pattern_name,
                        lookback, backtest_results, pattern_analysis):
    """保存排序预测结果"""

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    with open('ranking_prediction_results.txt', 'w', encoding='utf-8') as f:
        f.write(f"概率排序预测结果报告\n")
        f.write(f"生成时间: {timestamp}\n")
        f.write("=" * 80 + "\n\n")

        f.write(f"预测期号: {period}\n")
        f.write(f"预测红球: {red_balls}\n")
        f.write(f"预测蓝球: {blue_ball}\n")
        f.write(f"使用模式: {pattern_name}\n")
        f.write(f"回看期数: {lookback}\n\n")

        f.write("回归测试结果:\n")
        f.write("-" * 60 + "\n")
        for pattern_name, results in backtest_results['sorted_results']:
            f.write(f"{pattern_name}: 平均总分{results['avg_total_score']:.2f}, "
                   f"红球{results['avg_red_hits']:.2f}个, "
                   f"蓝球{results['blue_hit_rate']:.1f}%\n")

        f.write(f"\n排序模式分析:\n")
        f.write(f"总样本数: {pattern_analysis['total_samples']}\n")
        f.write(f"红球平均排序: {pattern_analysis['red_ranking_stats']['overall_stats']['mean']:.2f}\n")
        f.write(f"蓝球平均排序: {pattern_analysis['blue_ranking_stats']['mean']:.2f}\n")

    print(f"\n💾 详细结果已保存到 ranking_prediction_results.txt")

if __name__ == "__main__":
    result = main()

    if result:
        print(f"\n🎉 概率排序预测完成！")
        print(f"🏆 最佳模式: {result['best_pattern']}")
        print(f"📊 最佳回看期数: {result['best_lookback']}")
        print(f"🎯 预测第{result['next_period']}期: "
              f"红球{result['prediction']['red']} 蓝球{result['prediction']['blue']}")
