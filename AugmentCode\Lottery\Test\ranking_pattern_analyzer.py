#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排序模式深度分析器
专门分析和发现稳定的概率排序组合模式
"""

import numpy as np
import pandas as pd
from lottery_predictor import LotteryPredictor
from probability_ranking_predictor import ProbabilityRankingPredictor
from collections import defaultdict, Counter
import matplotlib.pyplot as plt
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class RankingPatternAnalyzer:
    """排序模式深度分析器"""
    
    def __init__(self, base_predictor):
        """初始化分析器"""
        self.base_predictor = base_predictor
        self.data = base_predictor.ssqhistory_all
        self.prob_predictor = ProbabilityRankingPredictor(base_predictor)
        
    def discover_stable_patterns(self, lookback_periods=75, min_samples=500):
        """
        发现稳定的排序模式
        
        Args:
            lookback_periods: 回看期数
            min_samples: 最小样本数
        
        Returns:
            stable_patterns: 稳定的排序模式
        """
        print(f"🔍 发现稳定排序模式 (回看{lookback_periods}期)...")
        
        total_periods = len(self.data)
        start_period = max(lookback_periods + 1, total_periods - min_samples)
        
        # 收集所有排序数据
        all_rankings = []
        ranking_sequences = []
        
        for period_idx in range(start_period, total_periods):
            if period_idx % 100 == 0:
                print(f"   进度: {period_idx - start_period + 1}/{total_periods - start_period}")
            
            # 计算概率表
            red_probs, blue_probs = self.prob_predictor.calculate_probability_table(
                period_idx, lookback_periods)
            
            # 获取实际排序
            red_rankings, blue_ranking = self.prob_predictor.get_actual_ranking(
                period_idx, red_probs, blue_probs)
            
            all_rankings.append({
                'period': int(self.data.iloc[period_idx]['期号']),
                'red_rankings': red_rankings,
                'blue_ranking': blue_ranking,
                'red_probs': red_probs,
                'blue_probs': blue_probs
            })
            
            # 记录排序序列（用于发现连续模式）
            ranking_sequences.append(tuple(red_rankings + [blue_ranking]))
        
        # 分析稳定模式
        stable_patterns = self._analyze_stability(all_rankings, ranking_sequences)
        
        return stable_patterns
    
    def _analyze_stability(self, all_rankings, ranking_sequences):
        """分析排序稳定性"""
        
        # 1. 分析排序组合频率
        combination_counter = Counter(ranking_sequences)
        total_samples = len(ranking_sequences)
        
        # 2. 分析各位置的稳定性
        position_stability = self._analyze_position_stability(all_rankings)
        
        # 3. 分析排序范围的稳定性
        range_stability = self._analyze_range_stability(all_rankings)
        
        # 4. 发现高频稳定模式
        stable_combinations = []
        for combination, count in combination_counter.most_common(50):
            frequency = count / total_samples
            if frequency > 0.001:  # 出现频率超过0.1%
                red_rankings = list(combination[:6])
                blue_ranking = combination[6]
                
                stable_combinations.append({
                    'red_rankings': red_rankings,
                    'blue_ranking': blue_ranking,
                    'frequency': frequency,
                    'count': count,
                    'stability_score': self._calculate_stability_score(
                        red_rankings, blue_ranking, position_stability)
                })
        
        # 按稳定性得分排序
        stable_combinations.sort(key=lambda x: x['stability_score'], reverse=True)
        
        return {
            'stable_combinations': stable_combinations,
            'position_stability': position_stability,
            'range_stability': range_stability,
            'total_samples': total_samples,
            'unique_combinations': len(combination_counter)
        }
    
    def _analyze_position_stability(self, all_rankings):
        """分析各位置的排序稳定性"""
        
        position_data = {f'红球位置{i+1}': [] for i in range(6)}
        position_data['蓝球位置'] = []
        
        for ranking_data in all_rankings:
            red_rankings = ranking_data['red_rankings']
            blue_ranking = ranking_data['blue_ranking']
            
            for i, rank in enumerate(red_rankings):
                position_data[f'红球位置{i+1}'].append(rank)
            
            position_data['蓝球位置'].append(blue_ranking)
        
        # 计算各位置的稳定性指标
        stability_metrics = {}
        for position, rankings in position_data.items():
            stability_metrics[position] = {
                'mean': np.mean(rankings),
                'std': np.std(rankings),
                'median': np.median(rankings),
                'mode': Counter(rankings).most_common(1)[0][0],
                'mode_frequency': Counter(rankings).most_common(1)[0][1] / len(rankings),
                'coefficient_of_variation': np.std(rankings) / np.mean(rankings) if np.mean(rankings) > 0 else 0
            }
        
        return stability_metrics
    
    def _analyze_range_stability(self, all_rankings):
        """分析排序范围的稳定性"""
        
        red_ranges = []
        red_spans = []
        
        for ranking_data in all_rankings:
            red_rankings = ranking_data['red_rankings']
            red_range = (min(red_rankings), max(red_rankings))
            red_span = max(red_rankings) - min(red_rankings)
            
            red_ranges.append(red_range)
            red_spans.append(red_span)
        
        # 分析最常见的范围
        range_counter = Counter(red_ranges)
        span_counter = Counter(red_spans)
        
        return {
            'common_ranges': range_counter.most_common(10),
            'common_spans': span_counter.most_common(10),
            'avg_span': np.mean(red_spans),
            'std_span': np.std(red_spans)
        }
    
    def _calculate_stability_score(self, red_rankings, blue_ranking, position_stability):
        """计算排序组合的稳定性得分"""
        
        score = 0
        
        # 基于各位置的稳定性评分
        for i, rank in enumerate(red_rankings):
            position_key = f'红球位置{i+1}'
            position_stats = position_stability[position_key]
            
            # 距离位置平均值越近，得分越高
            distance_from_mean = abs(rank - position_stats['mean'])
            normalized_distance = distance_from_mean / position_stats['std'] if position_stats['std'] > 0 else 0
            position_score = max(0, 1 - normalized_distance / 3)  # 3个标准差内得分
            
            score += position_score
        
        # 蓝球稳定性评分
        blue_stats = position_stability['蓝球位置']
        blue_distance = abs(blue_ranking - blue_stats['mean'])
        blue_normalized = blue_distance / blue_stats['std'] if blue_stats['std'] > 0 else 0
        blue_score = max(0, 1 - blue_normalized / 3)
        
        score += blue_score
        
        # 归一化到0-1
        return score / 7
    
    def test_pattern_performance(self, stable_patterns, test_periods=500):
        """测试稳定模式的预测性能"""
        
        print(f"🧪 测试稳定模式性能...")
        
        total_periods = len(self.data)
        start_period = total_periods - test_periods
        
        # 选择前10个最稳定的模式进行测试
        top_patterns = stable_patterns['stable_combinations'][:10]
        
        results = {}
        
        for i, pattern in enumerate(top_patterns):
            pattern_name = f"稳定模式{i+1}"
            print(f"   测试 {pattern_name}...")
            
            red_hits = []
            blue_hits = []
            
            for period_idx in range(start_period, total_periods):
                # 使用该模式预测
                pred_red, pred_blue = self.prob_predictor.predict_by_ranking(
                    period_idx, 75, pattern)
                
                # 获取实际结果
                actual_row = self.data.iloc[period_idx]
                actual_red = [int(actual_row[f'红球{j}']) for j in range(1, 7)]
                actual_blue = int(actual_row['蓝球'])
                
                # 计算命中
                red_hit = len(set(pred_red) & set(actual_red))
                blue_hit = 1 if pred_blue == actual_blue else 0
                
                red_hits.append(red_hit)
                blue_hits.append(blue_hit)
            
            results[pattern_name] = {
                'pattern': pattern,
                'avg_red_hits': np.mean(red_hits),
                'blue_hit_rate': np.mean(blue_hits) * 100,
                'avg_total_score': np.mean(red_hits) + np.mean(blue_hits) * 2,
                'stability_score': pattern['stability_score']
            }
        
        # 排序结果
        sorted_results = sorted(results.items(), 
                              key=lambda x: x[1]['avg_total_score'], reverse=True)
        
        return sorted_results
    
    def find_optimal_ranking_strategy(self, lookback_range=(50, 150, 25)):
        """寻找最优排序策略"""
        
        print(f"🎯 寻找最优排序策略...")
        
        lookback_start, lookback_end, lookback_step = lookback_range
        strategy_results = {}
        
        for lookback in range(lookback_start, lookback_end + 1, lookback_step):
            print(f"   测试回看期数: {lookback}")
            
            # 发现该回看期数下的稳定模式
            stable_patterns = self.discover_stable_patterns(lookback, min_samples=300)
            
            # 测试性能
            performance_results = self.test_pattern_performance(stable_patterns, test_periods=200)
            
            if performance_results:
                best_pattern_name, best_result = performance_results[0]
                
                strategy_results[lookback] = {
                    'best_pattern': best_result,
                    'best_pattern_name': best_pattern_name,
                    'stable_patterns_count': len(stable_patterns['stable_combinations']),
                    'avg_total_score': best_result['avg_total_score']
                }
        
        # 找出最优策略
        if strategy_results:
            optimal_lookback = max(strategy_results.keys(), 
                                 key=lambda k: strategy_results[k]['avg_total_score'])
            
            return {
                'optimal_lookback': optimal_lookback,
                'optimal_strategy': strategy_results[optimal_lookback],
                'all_strategies': strategy_results
            }
        
        return None
    
    def generate_final_prediction(self, optimal_strategy):
        """生成最终预测"""
        
        print(f"🎯 生成最终预测...")
        
        optimal_lookback = optimal_strategy['optimal_lookback']
        best_pattern = optimal_strategy['optimal_strategy']['best_pattern']['pattern']
        
        # 预测下一期
        if self.base_predictor.ssqhistory_all is not None:
            current_period_index = len(self.base_predictor.ssqhistory_all)
            latest_period = int(self.base_predictor.ssqhistory_all.iloc[-1]['期号'])
            next_period = latest_period + 1
            
            # 使用最优模式预测
            pred_red, pred_blue = self.prob_predictor.predict_by_ranking(
                current_period_index, optimal_lookback, best_pattern)
            
            # 转换为普通Python整数
            pred_red = [int(ball) for ball in pred_red]
            pred_blue = int(pred_blue)
            
            return {
                'period': next_period,
                'red_balls': pred_red,
                'blue_ball': pred_blue,
                'strategy': optimal_strategy,
                'pattern': best_pattern
            }
        
        return None

def main():
    """主函数"""
    print("=" * 80)
    print("🔬 排序模式深度分析系统")
    print("发现稳定的概率排序组合模式")
    print("=" * 80)
    
    # 初始化
    base_predictor = LotteryPredictor()
    if not base_predictor.load_data():
        print("❌ 数据加载失败！")
        return
    
    base_predictor.analyze_data()
    
    # 创建分析器
    analyzer = RankingPatternAnalyzer(base_predictor)
    
    # 寻找最优策略
    optimal_strategy = analyzer.find_optimal_ranking_strategy()
    
    if optimal_strategy:
        print("\n" + "=" * 80)
        print("🏆 最优排序策略")
        print("=" * 80)
        
        opt_lookback = optimal_strategy['optimal_lookback']
        opt_result = optimal_strategy['optimal_strategy']
        
        print(f"🎯 最优回看期数: {opt_lookback}")
        print(f"🏆 最佳模式: {opt_result['best_pattern_name']}")
        print(f"📊 平均总分: {opt_result['avg_total_score']:.2f}")
        print(f"🔴 平均红球命中: {opt_result['best_pattern']['avg_red_hits']:.2f}")
        print(f"🔵 蓝球命中率: {opt_result['best_pattern']['blue_hit_rate']:.1f}%")
        print(f"⭐ 稳定性得分: {opt_result['best_pattern']['stability_score']:.3f}")
        
        # 显示最佳排序模式
        best_pattern = opt_result['best_pattern']['pattern']
        print(f"\n📋 最佳排序模式:")
        print(f"   红球排序位置: {best_pattern['red_rankings']}")
        print(f"   蓝球排序位置: {best_pattern['blue_ranking']}")
        print(f"   出现频率: {best_pattern['frequency']:.4f} ({best_pattern['count']}次)")
        
        # 生成最终预测
        final_prediction = analyzer.generate_final_prediction(optimal_strategy)
        
        if final_prediction:
            print(f"\n🎊 第{final_prediction['period']}期最优排序预测:")
            print(f"🔴 红球: {final_prediction['red_balls']}")
            print(f"🔵 蓝球: {final_prediction['blue_ball']}")
            
            # 保存结果
            save_pattern_analysis_results(final_prediction, optimal_strategy)
            
            return final_prediction
    
    else:
        print("❌ 未找到有效的排序策略")
        return None

def save_pattern_analysis_results(prediction, optimal_strategy):
    """保存模式分析结果"""
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    with open('pattern_analysis_results.txt', 'w', encoding='utf-8') as f:
        f.write(f"排序模式深度分析结果\n")
        f.write(f"分析时间: {timestamp}\n")
        f.write("=" * 80 + "\n\n")
        
        f.write(f"最终预测:\n")
        f.write(f"期号: {prediction['period']}\n")
        f.write(f"红球: {prediction['red_balls']}\n")
        f.write(f"蓝球: {prediction['blue_ball']}\n\n")
        
        f.write(f"最优策略:\n")
        opt_result = optimal_strategy['optimal_strategy']
        f.write(f"回看期数: {optimal_strategy['optimal_lookback']}\n")
        f.write(f"最佳模式: {opt_result['best_pattern_name']}\n")
        f.write(f"平均总分: {opt_result['avg_total_score']:.2f}\n")
        f.write(f"稳定性得分: {opt_result['best_pattern']['stability_score']:.3f}\n\n")
        
        f.write(f"排序模式详情:\n")
        pattern = prediction['pattern']
        f.write(f"红球排序: {pattern['red_rankings']}\n")
        f.write(f"蓝球排序: {pattern['blue_ranking']}\n")
        f.write(f"出现频率: {pattern['frequency']:.4f}\n")
    
    print(f"\n💾 模式分析结果已保存到 pattern_analysis_results.txt")

if __name__ == "__main__":
    result = main()
    
    if result:
        print(f"\n🎉 排序模式分析完成！")
        print(f"🎯 预测第{result['period']}期: "
              f"红球{result['red_balls']} 蓝球{result['blue_ball']}")
    else:
        print(f"\n❌ 分析失败")
