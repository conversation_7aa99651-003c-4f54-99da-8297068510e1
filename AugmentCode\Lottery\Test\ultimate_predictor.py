#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极双色球预测系统
基于性能测试结果优化的最强预测模型
"""

from lottery_predictor import LotteryPredictor
from advanced_predictor import AdvancedLotteryPredictor
import numpy as np
import pandas as pd
from datetime import datetime

class UltimateLotteryPredictor:
    """终极双色球预测器"""
    
    def __init__(self):
        """初始化终极预测器"""
        self.base_predictor = LotteryPredictor()
        self.advanced_predictor = None
        
        # 基于性能测试的方法权重
        self.method_weights = {
            '时间序列': 0.25,      # 综合表现最佳
            '马尔可夫链(2阶)': 0.20,  # 蓝球预测最佳
            '集成预测': 0.15,      # 综合性能良好
            '统计学方法': 0.15,     # 稳定性好
            '贝叶斯概率': 0.15,     # 速度快，蓝球好
            '模式聚类': 0.05,      # 辅助方法
            '马尔可夫链(1阶)': 0.05   # 辅助方法
        }
        
        # 红球和蓝球分别的最佳方法
        self.best_red_methods = ['时间序列', '集成预测', '马尔可夫链(2阶)']
        self.best_blue_methods = ['马尔可夫链(2阶)', '贝叶斯概率', '时间序列']
    
    def initialize(self):
        """初始化预测器"""
        print("🚀 初始化终极预测系统...")
        
        if not self.base_predictor.load_data():
            print("❌ 数据加载失败！")
            return False
        
        self.base_predictor.analyze_data()
        self.base_predictor.calculate_intervals()
        
        self.advanced_predictor = AdvancedLotteryPredictor(self.base_predictor)
        
        # 预构建模型
        print("🔧 预构建高级模型...")
        self.advanced_predictor.build_markov_chain(order=1)
        self.advanced_predictor.build_markov_chain(order=2)
        self.advanced_predictor.pattern_clustering(n_clusters=8)
        
        print("✅ 终极预测系统初始化完成！")
        return True
    
    def weighted_ensemble_predict(self, period_index):
        """
        加权集成预测
        
        Args:
            period_index: 期数索引
        
        Returns:
            predicted_red, predicted_blue, confidence: 预测结果和置信度
        """
        print(f"🎯 使用终极算法预测第{period_index}期...")
        
        # 收集各方法预测
        method_predictions = {}
        
        try:
            # 时间序列方法
            red, blue = self.advanced_predictor.predict_with_time_series(period_index)
            method_predictions['时间序列'] = {'red': red, 'blue': blue}
        except:
            pass
        
        try:
            # 马尔可夫链方法
            red, blue = self.advanced_predictor.predict_with_markov(period_index, order=2)
            method_predictions['马尔可夫链(2阶)'] = {'red': red, 'blue': blue}
            
            red, blue = self.advanced_predictor.predict_with_markov(period_index, order=1)
            method_predictions['马尔可夫链(1阶)'] = {'red': red, 'blue': blue}
        except:
            pass
        
        try:
            # 贝叶斯方法
            red, blue = self.advanced_predictor.predict_with_bayesian(period_index)
            method_predictions['贝叶斯概率'] = {'red': red, 'blue': blue}
        except:
            pass
        
        try:
            # 聚类方法
            red, blue = self.advanced_predictor.predict_with_clustering(period_index)
            method_predictions['模式聚类'] = {'red': red, 'blue': blue}
        except:
            pass
        
        try:
            # 传统方法
            red, blue = self.base_predictor.predict_numbers_statistical(period_index)
            method_predictions['统计学方法'] = {'red': red, 'blue': blue}
        except:
            pass
        
        # 智能加权红球预测
        red_votes = {}
        red_total_weight = 0
        
        for method_name, pred in method_predictions.items():
            if method_name in self.method_weights:
                weight = self.method_weights[method_name]
                # 对红球预测最佳的方法给予额外权重
                if method_name in self.best_red_methods:
                    weight *= 1.5
                
                red_total_weight += weight
                
                for ball in pred['red']:
                    red_votes[ball] = red_votes.get(ball, 0) + weight
        
        # 智能加权蓝球预测
        blue_votes = {}
        blue_total_weight = 0
        
        for method_name, pred in method_predictions.items():
            if method_name in self.method_weights:
                weight = self.method_weights[method_name]
                # 对蓝球预测最佳的方法给予额外权重
                if method_name in self.best_blue_methods:
                    weight *= 1.8
                
                blue_total_weight += weight
                blue_votes[pred['blue']] = blue_votes.get(pred['blue'], 0) + weight
        
        # 选择红球
        if red_votes:
            # 按权重排序
            sorted_red = sorted(red_votes.items(), key=lambda x: x[1], reverse=True)
            
            # 智能选择：结合权重和随机性
            predicted_red = []
            available_balls = [ball for ball, weight in sorted_red]
            weights = [weight for ball, weight in sorted_red]
            
            # 归一化权重
            total_weight = sum(weights)
            if total_weight > 0:
                probs = [w/total_weight for w in weights]
            else:
                probs = [1/len(available_balls)] * len(available_balls)
            
            # 加权随机选择
            for _ in range(6):
                if len(available_balls) == 0:
                    break
                
                # 增加随机性，避免过度拟合
                if np.random.random() < 0.3:  # 30%的随机性
                    chosen_idx = np.random.choice(len(available_balls))
                else:
                    chosen_idx = np.random.choice(len(available_balls), p=probs[:len(available_balls)])
                
                chosen_ball = available_balls[chosen_idx]
                predicted_red.append(chosen_ball)
                
                # 移除已选择的球
                available_balls.pop(chosen_idx)
                probs = np.delete(probs, chosen_idx)
                if len(probs) > 0 and probs.sum() > 0:
                    probs = probs / probs.sum()
            
            # 补充不足的球
            if len(predicted_red) < 6:
                remaining = [i for i in range(1, 34) if i not in predicted_red]
                needed = 6 - len(predicted_red)
                predicted_red.extend(np.random.choice(remaining, needed, replace=False))
            
            predicted_red = sorted(predicted_red[:6])
        else:
            predicted_red = sorted(np.random.choice(range(1, 34), 6, replace=False))
        
        # 选择蓝球
        if blue_votes:
            # 加权随机选择蓝球
            blue_balls = list(blue_votes.keys())
            blue_weights = list(blue_votes.values())
            total_blue_weight = sum(blue_weights)
            
            if total_blue_weight > 0:
                blue_probs = [w/total_blue_weight for w in blue_weights]
                predicted_blue = np.random.choice(blue_balls, p=blue_probs)
            else:
                predicted_blue = np.random.choice(blue_balls)
        else:
            predicted_blue = np.random.choice(range(1, 17))
        
        # 计算置信度
        red_confidence = min(100, (red_total_weight / len(method_predictions)) * 100) if method_predictions else 0
        blue_confidence = min(100, (blue_total_weight / len(method_predictions)) * 100) if method_predictions else 0
        
        return predicted_red, predicted_blue, {
            'red_confidence': red_confidence,
            'blue_confidence': blue_confidence,
            'methods_used': list(method_predictions.keys()),
            'method_predictions': method_predictions
        }
    
    def adaptive_predict(self, period_index, recent_performance=None):
        """
        自适应预测（根据最近表现调整权重）
        
        Args:
            period_index: 期数索引
            recent_performance: 最近的性能数据
        
        Returns:
            predicted_red, predicted_blue, info: 预测结果和信息
        """
        # 如果有最近性能数据，调整权重
        if recent_performance:
            self._adjust_weights(recent_performance)
        
        # 分析最近趋势
        recent_data = self.base_predictor.ssqhistory_all.iloc[max(0, period_index-10):period_index]
        
        # 趋势分析
        trend_info = self._analyze_recent_trends(recent_data)
        
        # 基础预测
        pred_red, pred_blue, confidence = self.weighted_ensemble_predict(period_index)
        
        # 趋势调整
        adjusted_red, adjusted_blue = self._apply_trend_adjustment(
            pred_red, pred_blue, trend_info)
        
        return adjusted_red, adjusted_blue, {
            'base_prediction': {'red': pred_red, 'blue': pred_blue},
            'trend_adjustment': trend_info,
            'confidence': confidence,
            'final_prediction': {'red': adjusted_red, 'blue': adjusted_blue}
        }
    
    def _adjust_weights(self, recent_performance):
        """根据最近表现调整方法权重"""
        for method, performance in recent_performance.items():
            if method in self.method_weights:
                # 根据最近表现调整权重
                performance_score = performance.get('avg_total_score', 0)
                if performance_score > 1.2:  # 表现好的方法增加权重
                    self.method_weights[method] *= 1.1
                elif performance_score < 0.8:  # 表现差的方法减少权重
                    self.method_weights[method] *= 0.9
        
        # 重新归一化权重
        total_weight = sum(self.method_weights.values())
        for method in self.method_weights:
            self.method_weights[method] /= total_weight
    
    def _analyze_recent_trends(self, recent_data):
        """分析最近趋势"""
        if len(recent_data) < 3:
            return {'red_trend': 0, 'blue_trend': 0, 'pattern': 'insufficient_data'}
        
        # 红球趋势
        red_sums = []
        for _, row in recent_data.iterrows():
            red_balls = [int(row[f'红球{i}']) for i in range(1, 7)]
            red_sums.append(sum(red_balls))
        
        red_trend = np.polyfit(range(len(red_sums)), red_sums, 1)[0]
        
        # 蓝球趋势
        blue_values = recent_data['蓝球'].values
        blue_trend = np.polyfit(range(len(blue_values)), blue_values, 1)[0]
        
        # 模式识别
        pattern = 'normal'
        if abs(red_trend) > 5:
            pattern = 'red_trending'
        if abs(blue_trend) > 2:
            pattern = 'blue_trending'
        
        return {
            'red_trend': red_trend,
            'blue_trend': blue_trend,
            'pattern': pattern
        }
    
    def _apply_trend_adjustment(self, pred_red, pred_blue, trend_info):
        """应用趋势调整"""
        adjusted_red = pred_red.copy()
        adjusted_blue = pred_blue
        
        # 根据趋势微调预测
        if trend_info['pattern'] == 'red_trending':
            # 红球有明显趋势时，适当调整
            if trend_info['red_trend'] > 0:  # 上升趋势
                # 倾向于选择较大的号码
                for i in range(len(adjusted_red)):
                    if adjusted_red[i] < 17 and np.random.random() < 0.3:
                        available_large = [x for x in range(17, 34) if x not in adjusted_red]
                        if available_large:
                            adjusted_red[i] = np.random.choice(available_large)
            else:  # 下降趋势
                # 倾向于选择较小的号码
                for i in range(len(adjusted_red)):
                    if adjusted_red[i] > 16 and np.random.random() < 0.3:
                        available_small = [x for x in range(1, 17) if x not in adjusted_red]
                        if available_small:
                            adjusted_red[i] = np.random.choice(available_small)
        
        if trend_info['pattern'] == 'blue_trending':
            # 蓝球有趋势时调整
            if trend_info['blue_trend'] > 0:
                adjusted_blue = min(16, adjusted_blue + 1)
            else:
                adjusted_blue = max(1, adjusted_blue - 1)
        
        return sorted(adjusted_red), adjusted_blue

def ultimate_prediction():
    """终极预测主函数"""
    
    print("=" * 80)
    print("🏆 终极双色球预测系统 🏆")
    print("基于性能测试优化的最强算法")
    print("=" * 80)
    
    # 创建终极预测器
    ultimate = UltimateLotteryPredictor()
    
    if not ultimate.initialize():
        return
    
    # 获取最新期号
    latest_row = ultimate.base_predictor.ssqhistory_all.iloc[-1]
    latest_period = int(latest_row['期号'])
    next_period = latest_period + 1
    current_period_index = len(ultimate.base_predictor.ssqhistory_all)
    
    print(f"\n📅 最新开奖期号: {latest_period}")
    print(f"🎯 预测目标期号: {next_period}")
    
    # 执行终极预测
    print(f"\n🚀 执行终极预测算法...")
    
    final_red, final_blue, prediction_info = ultimate.adaptive_predict(current_period_index)
    
    # 显示结果
    print("\n" + "=" * 80)
    print("🏆 终极预测结果")
    print("=" * 80)
    
    print(f"\n🎯 第 {next_period} 期终极推荐:")
    print(f"🔴 红球: {final_red}")
    print(f"🔵 蓝球: {final_blue}")
    
    print(f"\n📊 预测置信度:")
    confidence = prediction_info['confidence']
    print(f"   红球置信度: {confidence['red_confidence']:.1f}%")
    print(f"   蓝球置信度: {confidence['blue_confidence']:.1f}%")
    print(f"   使用方法数: {len(confidence['methods_used'])}")
    
    print(f"\n🔍 各方法预测详情:")
    for method, pred in confidence['method_predictions'].items():
        weight = ultimate.method_weights.get(method, 0) * 100
        print(f"   {method} (权重{weight:.1f}%): 红球{pred['red']} 蓝球{pred['blue']}")
    
    # 趋势分析
    trend = prediction_info['trend_adjustment']
    print(f"\n📈 趋势分析:")
    print(f"   红球趋势: {trend['red_trend']:.2f}")
    print(f"   蓝球趋势: {trend['blue_trend']:.2f}")
    print(f"   模式识别: {trend['pattern']}")
    
    # 保存终极预测
    save_ultimate_prediction(next_period, final_red, final_blue, prediction_info)
    
    print("\n" + "=" * 80)
    print("🎉 终极预测完成！")
    print("基于8种科学方法的智能加权集成算法")
    print("祝您好运！🍀")
    print("=" * 80)
    
    return {
        'period': next_period,
        'red': final_red,
        'blue': final_blue,
        'info': prediction_info
    }

def save_ultimate_prediction(period, red_balls, blue_ball, info):
    """保存终极预测结果"""
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    with open('ultimate_predictions.txt', 'a', encoding='utf-8') as f:
        f.write(f"\n{'='*80}\n")
        f.write(f"终极预测记录 - {timestamp}\n")
        f.write(f"{'='*80}\n")
        f.write(f"预测期号: {period}\n")
        f.write(f"终极红球: {red_balls}\n")
        f.write(f"终极蓝球: {blue_ball}\n\n")
        
        f.write("置信度信息:\n")
        confidence = info['confidence']
        f.write(f"  红球置信度: {confidence['red_confidence']:.1f}%\n")
        f.write(f"  蓝球置信度: {confidence['blue_confidence']:.1f}%\n")
        f.write(f"  使用方法: {confidence['methods_used']}\n\n")
        
        f.write("趋势分析:\n")
        trend = info['trend_adjustment']
        f.write(f"  红球趋势: {trend['red_trend']:.2f}\n")
        f.write(f"  蓝球趋势: {trend['blue_trend']:.2f}\n")
        f.write(f"  模式: {trend['pattern']}\n")
    
    print(f"\n💾 终极预测结果已保存到 ultimate_predictions.txt")

if __name__ == "__main__":
    result = ultimate_prediction()
    
    if result:
        print(f"\n🎊 预测第{result['period']}期: 红球{result['red']} 蓝球{result['blue']}")
