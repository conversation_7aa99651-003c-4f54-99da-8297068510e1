#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双色球数据可视化分析脚本
"""

from lottery_predictor import LotteryPredictor
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def create_visualizations():
    """创建各种可视化图表"""
    
    print("📊 正在生成数据可视化图表...")
    
    # 创建预测器并加载数据
    predictor = LotteryPredictor()
    if not predictor.load_data():
        print("❌ 数据加载失败！")
        return
    
    predictor.analyze_data()
    predictor.calculate_intervals()
    
    # 创建图表
    fig = plt.figure(figsize=(20, 15))
    
    # 1. 红球出现频率分布
    plt.subplot(3, 3, 1)
    red_freq = predictor.red_ball_stats['frequency']
    balls = list(red_freq.keys())
    frequencies = list(red_freq.values())
    
    plt.bar(balls, frequencies, color='red', alpha=0.7)
    plt.title('红球号码出现频率分布')
    plt.xlabel('红球号码')
    plt.ylabel('出现次数')
    plt.xticks(range(1, 34, 2))
    
    # 2. 蓝球出现频率分布
    plt.subplot(3, 3, 2)
    blue_freq = predictor.blue_ball_stats['frequency']
    blue_balls = list(blue_freq.keys())
    blue_frequencies = list(blue_freq.values())
    
    plt.bar(blue_balls, blue_frequencies, color='blue', alpha=0.7)
    plt.title('蓝球号码出现频率分布')
    plt.xlabel('蓝球号码')
    plt.ylabel('出现次数')
    plt.xticks(range(1, 17))
    
    # 3. 红球号码热力图
    plt.subplot(3, 3, 3)
    red_matrix = np.zeros((6, 6))
    for i in range(len(predictor.ssqhistory_all)):
        row = predictor.ssqhistory_all.iloc[i]
        red_balls = [int(row[f'红球{j}']) for j in range(1, 7)]
        for r, ball in enumerate(red_balls):
            red_matrix[r][ball % 6] += 1
    
    sns.heatmap(red_matrix, annot=True, fmt='.0f', cmap='Reds')
    plt.title('红球位置分布热力图')
    plt.xlabel('号码模6余数')
    plt.ylabel('球位置')
    
    # 4. 最近100期红球趋势
    plt.subplot(3, 3, 4)
    recent_data = predictor.ssqhistory_all.tail(100)
    periods = recent_data['期号'].values
    
    # 计算每期红球的平均值
    red_averages = []
    for _, row in recent_data.iterrows():
        red_balls = [int(row[f'红球{i}']) for i in range(1, 7)]
        red_averages.append(np.mean(red_balls))
    
    plt.plot(range(len(red_averages)), red_averages, 'r-', linewidth=2)
    plt.title('最近100期红球平均值趋势')
    plt.xlabel('期数（倒数）')
    plt.ylabel('红球平均值')
    plt.grid(True, alpha=0.3)
    
    # 5. 蓝球趋势
    plt.subplot(3, 3, 5)
    blue_values = recent_data['蓝球'].values
    plt.plot(range(len(blue_values)), blue_values, 'b-', linewidth=2)
    plt.title('最近100期蓝球趋势')
    plt.xlabel('期数（倒数）')
    plt.ylabel('蓝球号码')
    plt.grid(True, alpha=0.3)
    
    # 6. 红球间隔分析
    plt.subplot(3, 3, 6)
    intervals = predictor.red_ball_stats.get('intervals', {})
    if intervals:
        balls = []
        avg_intervals = []
        for ball, data in intervals.items():
            balls.append(ball)
            avg_intervals.append(data['avg_interval'])
        
        plt.scatter(balls, avg_intervals, c='red', alpha=0.6)
        plt.title('红球平均间隔分析')
        plt.xlabel('红球号码')
        plt.ylabel('平均间隔（期）')
        plt.grid(True, alpha=0.3)
    
    # 7. 号码组合分析
    plt.subplot(3, 3, 7)
    # 分析奇偶比例
    odd_even_ratio = []
    for _, row in predictor.ssqhistory_all.iterrows():
        red_balls = [int(row[f'红球{i}']) for i in range(1, 7)]
        odd_count = sum(1 for ball in red_balls if ball % 2 == 1)
        odd_even_ratio.append(odd_count)
    
    plt.hist(odd_even_ratio, bins=7, color='purple', alpha=0.7, edgecolor='black')
    plt.title('红球奇数个数分布')
    plt.xlabel('奇数个数')
    plt.ylabel('出现次数')
    plt.xticks(range(7))
    
    # 8. 大小号比例分析
    plt.subplot(3, 3, 8)
    big_small_ratio = []
    for _, row in predictor.ssqhistory_all.iterrows():
        red_balls = [int(row[f'红球{i}']) for i in range(1, 7)]
        big_count = sum(1 for ball in red_balls if ball > 16)
        big_small_ratio.append(big_count)
    
    plt.hist(big_small_ratio, bins=7, color='orange', alpha=0.7, edgecolor='black')
    plt.title('红球大号(>16)个数分布')
    plt.xlabel('大号个数')
    plt.ylabel('出现次数')
    plt.xticks(range(7))
    
    # 9. 连号分析
    plt.subplot(3, 3, 9)
    consecutive_count = []
    for _, row in predictor.ssqhistory_all.iterrows():
        red_balls = sorted([int(row[f'红球{i}']) for i in range(1, 7)])
        consecutive = 0
        for i in range(len(red_balls) - 1):
            if red_balls[i+1] - red_balls[i] == 1:
                consecutive += 1
        consecutive_count.append(consecutive)
    
    plt.hist(consecutive_count, bins=6, color='green', alpha=0.7, edgecolor='black')
    plt.title('连号对数分布')
    plt.xlabel('连号对数')
    plt.ylabel('出现次数')
    plt.xticks(range(6))
    
    plt.tight_layout()
    plt.savefig('lottery_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 可视化图表已生成并保存为 lottery_analysis.png")

def generate_statistics_report():
    """生成统计分析报告"""
    
    print("\n📋 正在生成统计分析报告...")
    
    predictor = LotteryPredictor()
    if not predictor.load_data():
        return
    
    predictor.analyze_data()
    predictor.calculate_intervals()
    
    report = []
    report.append("=" * 80)
    report.append("双色球历史数据统计分析报告")
    report.append("=" * 80)
    
    # 基本统计
    total_periods = len(predictor.ssqhistory_all)
    report.append(f"\n📊 基本统计信息:")
    report.append(f"   总期数: {total_periods}")
    report.append(f"   数据范围: {predictor.ssqhistory_all.iloc[0]['期号']} - {predictor.ssqhistory_all.iloc[-1]['期号']}")
    
    # 红球统计
    red_freq = predictor.red_ball_stats['frequency']
    report.append(f"\n🔴 红球统计:")
    report.append(f"   平均值: {predictor.red_ball_stats['mean']:.2f}")
    report.append(f"   标准差: {predictor.red_ball_stats['std']:.2f}")
    
    # 最热门和最冷门的红球
    sorted_red = sorted(red_freq.items(), key=lambda x: x[1], reverse=True)
    report.append(f"   最热门红球: {sorted_red[:5]}")
    report.append(f"   最冷门红球: {sorted_red[-5:]}")
    
    # 蓝球统计
    blue_freq = predictor.blue_ball_stats['frequency']
    report.append(f"\n🔵 蓝球统计:")
    report.append(f"   平均值: {predictor.blue_ball_stats['mean']:.2f}")
    report.append(f"   标准差: {predictor.blue_ball_stats['std']:.2f}")
    
    sorted_blue = sorted(blue_freq.items(), key=lambda x: x[1], reverse=True)
    report.append(f"   最热门蓝球: {sorted_blue[:5]}")
    report.append(f"   最冷门蓝球: {sorted_blue[-5:]}")
    
    # 特殊模式分析
    report.append(f"\n🔍 特殊模式分析:")
    
    # 奇偶分析
    odd_counts = []
    for _, row in predictor.ssqhistory_all.iterrows():
        red_balls = [int(row[f'红球{i}']) for i in range(1, 7)]
        odd_count = sum(1 for ball in red_balls if ball % 2 == 1)
        odd_counts.append(odd_count)
    
    most_common_odd = max(set(odd_counts), key=odd_counts.count)
    report.append(f"   最常见奇数个数: {most_common_odd} (出现{odd_counts.count(most_common_odd)}次)")
    
    # 大小号分析
    big_counts = []
    for _, row in predictor.ssqhistory_all.iterrows():
        red_balls = [int(row[f'红球{i}']) for i in range(1, 7)]
        big_count = sum(1 for ball in red_balls if ball > 16)
        big_counts.append(big_count)
    
    most_common_big = max(set(big_counts), key=big_counts.count)
    report.append(f"   最常见大号个数: {most_common_big} (出现{big_counts.count(most_common_big)}次)")
    
    # 保存报告
    report_text = '\n'.join(report)
    with open('analysis_report.txt', 'w', encoding='utf-8') as f:
        f.write(report_text)
    
    print(report_text)
    print(f"\n💾 分析报告已保存到 analysis_report.txt")

if __name__ == "__main__":
    # 生成可视化图表
    create_visualizations()
    
    # 生成统计报告
    generate_statistics_report()
