# 双色球智能预测系统

基于历史数据的统计学分析和机器学习方法的双色球号码预测系统。

## 🎯 功能特点

- **数据读取**: 自动读取Excel文件中的历史双色球数据
- **统计分析**: 基于正态分布、概率分析、中心极限定理等统计学方法
- **机器学习**: 使用随机森林等机器学习算法进行预测
- **回归测试**: 对历史数据进行回归测试，验证算法准确性
- **可视化分析**: 生成各种统计图表和分析报告
- **预测记录**: 自动保存预测结果，便于跟踪准确性

## 📁 文件说明

### 核心文件
- `lottery_predictor.py` - 主要的预测系统类
- `predict_next.py` - 预测下一期号码的脚本
- `visualize_analysis.py` - 数据可视化分析脚本
- `lottery_data_all.xlsx` - 历史数据文件

### 输出文件
- `predictions.csv` - 预测结果记录
- `lottery_analysis.png` - 可视化分析图表
- `analysis_report.txt` - 统计分析报告

## 🚀 使用方法

### 1. 环境准备
```bash
pip install pandas numpy matplotlib seaborn scipy scikit-learn openpyxl
```

### 2. 预测下一期号码
```bash
python predict_next.py
```

### 3. 生成可视化分析
```bash
python visualize_analysis.py
```

### 4. 运行完整测试
```bash
python lottery_predictor.py
```

## 📊 预测方法

### 统计学方法
- **频率分析**: 基于历史出现频率计算权重
- **间隔分析**: 考虑号码距离上次出现的间隔
- **正态分布**: 基于号码分布的统计特性
- **加权随机**: 使用加权概率进行号码选择

### 机器学习方法
- **特征工程**: 提取历史数据的多维特征
- **随机森林**: 使用集成学习方法进行预测
- **时序分析**: 考虑时间序列的趋势特征

## 📈 系统性能

根据回归测试结果（最近100期）：

### 统计学方法
- 平均红球命中: 1.29个
- 蓝球命中率: 5.00%
- 期望收益率: -77.50%

### 机器学习方法
- 平均红球命中: 0.93个
- 蓝球命中率: 4.00%
- 期望收益率: -90.00%

## 🔍 数据分析

### 历史数据统计
- 总期数: 3333期
- 数据范围: 第3001期 - 第25087期
- 红球平均值: 16.84
- 蓝球平均值: 8.57

### 热门号码
- 红球最热门: 14号(659次), 26号(651次), 22号(646次)
- 蓝球最热门: 1号(227次), 15号(226次), 16号(225次)

## ⚠️ 重要提示

1. **随机性**: 彩票具有随机性，任何预测方法都无法保证中奖
2. **仅供参考**: 本系统仅供学习和参考，不构成投注建议
3. **理性购彩**: 请理性购彩，切勿沉迷
4. **风险控制**: 建议小额投注，做好风险控制

## 🛠️ 技术架构

```
LotteryPredictor (主类)
├── load_data() - 数据加载
├── analyze_data() - 统计分析
├── calculate_intervals() - 间隔计算
├── extract_features() - 特征提取
├── predict_numbers_statistical() - 统计学预测
├── predict_numbers_ml() - 机器学习预测
├── backtest() - 回归测试
└── calculate_expected_return() - 收益计算
```

## 📝 更新日志

### v1.0.0 (2025-08-03)
- 实现基础的数据读取和预处理功能
- 添加统计学分析方法
- 实现机器学习预测算法
- 完成回归测试系统
- 添加可视化分析功能
- 创建用户友好的预测界面

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目仅供学习和研究使用。

---

**免责声明**: 本系统仅供学习和研究使用，不保证预测准确性。彩票投注有风险，请理性购彩。
