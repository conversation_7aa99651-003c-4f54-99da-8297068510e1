高级预测方法性能测试报告
测试时间: 2025-08-03 21:11:32
测试样本: 100期
================================================================================

性能排名 (按平均总分):
--------------------------------------------------------------------------------
1. 时间序列
   平均红球命中: 1.20 ± 0.91
   蓝球命中率: 7.9%
   平均总分: 1.36
   平均执行时间: 0.043秒
   最高红球命中: 4个

2. 马尔可夫链(2阶)
   平均红球命中: 1.15 ± 0.75
   蓝球命中率: 7.9%
   平均总分: 1.31
   平均执行时间: 0.004秒
   最高红球命中: 3个

3. 集成预测
   平均红球命中: 1.20 ± 0.88
   蓝球命中率: 4.0%
   平均总分: 1.28
   平均执行时间: 1.928秒
   最高红球命中: 4个

4. 统计学方法
   平均红球命中: 1.09 ± 0.85
   蓝球命中率: 5.9%
   平均总分: 1.21
   平均执行时间: 0.011秒
   最高红球命中: 3个

5. 贝叶斯概率
   平均红球命中: 1.00 ± 0.83
   蓝球命中率: 7.9%
   平均总分: 1.16
   平均执行时间: 0.002秒
   最高红球命中: 3个

6. 模式聚类
   平均红球命中: 0.98 ± 0.96
   蓝球命中率: 5.0%
   平均总分: 1.08
   平均执行时间: 0.049秒
   最高红球命中: 4个

7. 马尔可夫链(1阶)
   平均红球命中: 0.96 ± 0.81
   蓝球命中率: 5.0%
   平均总分: 1.06
   平均执行时间: 0.004秒
   最高红球命中: 3个

8. 机器学习
   平均红球命中: 0.93 ± 0.73
   蓝球命中率: 4.0%
   平均总分: 1.01
   平均执行时间: 1.829秒
   最高红球命中: 3个


详细统计分析:
--------------------------------------------------------------------------------
综合表现最佳: 时间序列
红球预测最佳: 时间序列 (平均1.20个)
蓝球预测最佳: 马尔可夫链(2阶) (7.9%)
执行速度最快: 贝叶斯概率 (0.002秒)
