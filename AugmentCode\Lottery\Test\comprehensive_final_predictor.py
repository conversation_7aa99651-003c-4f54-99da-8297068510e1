#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合最终预测系统
整合所有预测方法的终极预测器
"""

import numpy as np
import pandas as pd
from lottery_predictor import LotteryPredictor
from advanced_predictor import AdvancedLotteryPredictor
from probability_ranking_predictor import ProbabilityRankingPredictor
from ranking_pattern_analyzer import RankingPatternAnalyzer
from ultimate_predictor import UltimateLotteryPredictor
from datetime import datetime

class ComprehensiveFinalPredictor:
    """综合最终预测器"""
    
    def __init__(self):
        """初始化综合预测器"""
        self.base_predictor = LotteryPredictor()
        self.advanced_predictor = None
        self.prob_ranking_predictor = None
        self.pattern_analyzer = None
        self.ultimate_predictor = UltimateLotteryPredictor()
        
        # 各方法的权重（基于性能测试结果）
        self.method_weights = {
            '概率排序方法': 0.30,    # 新方法，表现优异
            '时间序列方法': 0.25,    # 之前测试最佳
            '马尔可夫链方法': 0.20,   # 蓝球预测好
            '贝叶斯方法': 0.15,      # 速度快，稳定
            '统计学方法': 0.10       # 基础方法
        }
    
    def initialize(self):
        """初始化所有预测器"""
        print("🚀 初始化综合预测系统...")
        
        # 初始化基础预测器
        if not self.base_predictor.load_data():
            print("❌ 数据加载失败！")
            return False
        
        self.base_predictor.analyze_data()
        self.base_predictor.calculate_intervals()
        
        # 初始化高级预测器
        self.advanced_predictor = AdvancedLotteryPredictor(self.base_predictor)
        self.advanced_predictor.build_markov_chain(order=1)
        self.advanced_predictor.build_markov_chain(order=2)
        self.advanced_predictor.pattern_clustering(n_clusters=8)
        
        # 初始化概率排序预测器
        self.prob_ranking_predictor = ProbabilityRankingPredictor(self.base_predictor)
        self.prob_ranking_predictor.optimal_lookback = 100  # 基于测试结果
        
        # 初始化模式分析器
        self.pattern_analyzer = RankingPatternAnalyzer(self.base_predictor)
        
        # 初始化终极预测器
        if not self.ultimate_predictor.initialize():
            print("⚠️ 终极预测器初始化失败")
        
        print("✅ 综合预测系统初始化完成！")
        return True
    
    def comprehensive_predict(self, period_index):
        """
        综合预测方法
        
        Args:
            period_index: 期数索引
        
        Returns:
            final_prediction: 最终预测结果
        """
        print(f"🎯 执行综合预测算法...")
        
        all_predictions = {}
        
        try:
            # 1. 概率排序方法（新方法）
            print("   📊 概率排序方法...")
            # 使用最佳排序模式
            best_pattern = {
                'red_rankings': [2, 11, 14, 17, 21, 28],
                'blue_ranking': 8
            }
            red, blue = self.prob_ranking_predictor.predict_by_ranking(
                period_index, 100, best_pattern)
            all_predictions['概率排序方法'] = {'red': [int(x) for x in red], 'blue': int(blue)}
            
        except Exception as e:
            print(f"   ⚠️ 概率排序方法失败: {e}")
        
        try:
            # 2. 时间序列方法
            print("   📈 时间序列方法...")
            red, blue = self.advanced_predictor.predict_with_time_series(period_index)
            all_predictions['时间序列方法'] = {'red': red, 'blue': blue}
            
        except Exception as e:
            print(f"   ⚠️ 时间序列方法失败: {e}")
        
        try:
            # 3. 马尔可夫链方法
            print("   🔗 马尔可夫链方法...")
            red, blue = self.advanced_predictor.predict_with_markov(period_index, order=2)
            all_predictions['马尔可夫链方法'] = {'red': red, 'blue': blue}
            
        except Exception as e:
            print(f"   ⚠️ 马尔可夫链方法失败: {e}")
        
        try:
            # 4. 贝叶斯方法
            print("   🎯 贝叶斯方法...")
            red, blue = self.advanced_predictor.predict_with_bayesian(period_index)
            all_predictions['贝叶斯方法'] = {'red': red, 'blue': blue}
            
        except Exception as e:
            print(f"   ⚠️ 贝叶斯方法失败: {e}")
        
        try:
            # 5. 统计学方法
            print("   📊 统计学方法...")
            red, blue = self.base_predictor.predict_numbers_statistical(period_index)
            all_predictions['统计学方法'] = {'red': red, 'blue': blue}
            
        except Exception as e:
            print(f"   ⚠️ 统计学方法失败: {e}")
        
        # 智能集成预测
        final_prediction = self._intelligent_ensemble(all_predictions)
        
        return final_prediction, all_predictions
    
    def _intelligent_ensemble(self, all_predictions):
        """智能集成预测"""
        
        if not all_predictions:
            # 如果所有方法都失败，使用随机预测
            return {
                'red': sorted(np.random.choice(range(1, 34), 6, replace=False)),
                'blue': np.random.choice(range(1, 17))
            }
        
        # 红球集成
        red_votes = {}
        red_total_weight = 0
        
        for method_name, pred in all_predictions.items():
            if method_name in self.method_weights:
                weight = self.method_weights[method_name]
                red_total_weight += weight
                
                for ball in pred['red']:
                    red_votes[ball] = red_votes.get(ball, 0) + weight
        
        # 蓝球集成
        blue_votes = {}
        blue_total_weight = 0
        
        for method_name, pred in all_predictions.items():
            if method_name in self.method_weights:
                weight = self.method_weights[method_name]
                blue_total_weight += weight
                blue_votes[pred['blue']] = blue_votes.get(pred['blue'], 0) + weight
        
        # 选择红球（智能加权选择）
        if red_votes:
            sorted_red = sorted(red_votes.items(), key=lambda x: x[1], reverse=True)
            
            # 使用加权随机选择，避免过度确定性
            predicted_red = []
            available_balls = [ball for ball, weight in sorted_red]
            weights = [weight for ball, weight in sorted_red]
            
            # 归一化权重
            total_weight = sum(weights)
            if total_weight > 0:
                probs = [w/total_weight for w in weights]
            else:
                probs = [1/len(available_balls)] * len(available_balls)
            
            # 选择6个红球
            for _ in range(6):
                if len(available_balls) == 0:
                    break
                
                # 70%按权重选择，30%随机选择
                if np.random.random() < 0.7 and len(probs) > 0:
                    chosen_idx = np.random.choice(len(available_balls), p=probs[:len(available_balls)])
                else:
                    chosen_idx = np.random.choice(len(available_balls))
                
                chosen_ball = available_balls[chosen_idx]
                predicted_red.append(chosen_ball)
                
                # 移除已选择的球
                available_balls.pop(chosen_idx)
                probs = np.delete(probs, chosen_idx)
                if len(probs) > 0 and probs.sum() > 0:
                    probs = probs / probs.sum()
            
            # 补充不足的球
            if len(predicted_red) < 6:
                remaining = [i for i in range(1, 34) if i not in predicted_red]
                needed = 6 - len(predicted_red)
                predicted_red.extend(np.random.choice(remaining, needed, replace=False))
            
            predicted_red = sorted(predicted_red[:6])
        else:
            predicted_red = sorted(np.random.choice(range(1, 34), 6, replace=False))
        
        # 选择蓝球
        if blue_votes:
            # 加权随机选择蓝球
            blue_balls = list(blue_votes.keys())
            blue_weights = list(blue_votes.values())
            total_blue_weight = sum(blue_weights)
            
            if total_blue_weight > 0:
                blue_probs = [w/total_blue_weight for w in blue_weights]
                predicted_blue = np.random.choice(blue_balls, p=blue_probs)
            else:
                predicted_blue = np.random.choice(blue_balls)
        else:
            predicted_blue = np.random.choice(range(1, 17))
        
        return {
            'red': predicted_red,
            'blue': int(predicted_blue)
        }
    
    def calculate_prediction_confidence(self, all_predictions):
        """计算预测置信度"""
        
        if not all_predictions:
            return {'red': 0, 'blue': 0, 'overall': 0}
        
        # 红球一致性分析
        all_red = []
        for pred in all_predictions.values():
            all_red.extend(pred['red'])
        
        red_consensus = {}
        for ball in all_red:
            red_consensus[ball] = red_consensus.get(ball, 0) + 1
        
        # 计算红球置信度（基于最高支持度）
        if red_consensus:
            max_support = max(red_consensus.values())
            red_confidence = min(100, max_support / len(all_predictions) * 100)
        else:
            red_confidence = 0
        
        # 蓝球一致性分析
        blue_predictions = [pred['blue'] for pred in all_predictions.values()]
        blue_consensus = {}
        for ball in blue_predictions:
            blue_consensus[ball] = blue_consensus.get(ball, 0) + 1
        
        if blue_consensus:
            max_blue_support = max(blue_consensus.values())
            blue_confidence = min(100, max_blue_support / len(all_predictions) * 100)
        else:
            blue_confidence = 0
        
        overall_confidence = (red_confidence + blue_confidence) / 2
        
        return {
            'red': red_confidence,
            'blue': blue_confidence,
            'overall': overall_confidence
        }

def main():
    """主函数"""
    print("=" * 80)
    print("🏆 综合最终预测系统")
    print("整合所有预测方法的终极预测器")
    print("=" * 80)
    
    # 创建综合预测器
    comprehensive = ComprehensiveFinalPredictor()
    
    if not comprehensive.initialize():
        return
    
    # 获取当前期号信息
    if comprehensive.base_predictor.ssqhistory_all is not None:
        latest_row = comprehensive.base_predictor.ssqhistory_all.iloc[-1]
        latest_period = int(latest_row['期号'])
        latest_red = [int(latest_row[f'红球{i}']) for i in range(1, 7)]
        latest_blue = int(latest_row['蓝球'])
        
        print(f"\n📅 最新开奖期号: {latest_period}")
        print(f"🔴 最新红球号码: {latest_red}")
        print(f"🔵 最新蓝球号码: {latest_blue}")
        
        current_period_index = len(comprehensive.base_predictor.ssqhistory_all)
        next_period = latest_period + 1
        
        # 执行综合预测
        print(f"\n🚀 执行综合预测算法...")
        final_prediction, all_predictions = comprehensive.comprehensive_predict(current_period_index)
        
        # 计算置信度
        confidence = comprehensive.calculate_prediction_confidence(all_predictions)
        
        # 显示结果
        print("\n" + "=" * 80)
        print("🏆 综合最终预测结果")
        print("=" * 80)
        
        print(f"\n🎯 第 {next_period} 期综合预测:")
        print(f"🔴 红球: {final_prediction['red']}")
        print(f"🔵 蓝球: {final_prediction['blue']}")
        
        print(f"\n📊 预测置信度:")
        print(f"   红球置信度: {confidence['red']:.1f}%")
        print(f"   蓝球置信度: {confidence['blue']:.1f}%")
        print(f"   综合置信度: {confidence['overall']:.1f}%")
        
        print(f"\n🔍 各方法预测详情:")
        for method, pred in all_predictions.items():
            weight = comprehensive.method_weights.get(method, 0) * 100
            print(f"   {method} (权重{weight:.0f}%): 红球{pred['red']} 蓝球{pred['blue']}")
        
        # 一致性分析
        print(f"\n📈 预测一致性分析:")
        
        # 红球一致性
        all_red = []
        for pred in all_predictions.values():
            all_red.extend(pred['red'])
        
        red_consensus = {}
        for ball in all_red:
            red_consensus[ball] = red_consensus.get(ball, 0) + 1
        
        print("🔴 红球支持度排名:")
        sorted_red_consensus = sorted(red_consensus.items(), key=lambda x: x[1], reverse=True)
        for i, (ball, count) in enumerate(sorted_red_consensus[:10]):
            support_rate = count / len(all_predictions) * 100
            print(f"   {i+1:2d}. {ball:2d}号: {count}个方法支持 ({support_rate:.1f}%)")
        
        # 蓝球一致性
        blue_predictions = [pred['blue'] for pred in all_predictions.values()]
        blue_consensus = {}
        for ball in blue_predictions:
            blue_consensus[ball] = blue_consensus.get(ball, 0) + 1
        
        print("\n🔵 蓝球支持度排名:")
        sorted_blue_consensus = sorted(blue_consensus.items(), key=lambda x: x[1], reverse=True)
        for i, (ball, count) in enumerate(sorted_blue_consensus[:5]):
            support_rate = count / len(all_predictions) * 100
            print(f"   {i+1}. {ball:2d}号: {count}个方法支持 ({support_rate:.1f}%)")
        
        # 保存最终结果
        save_comprehensive_results(next_period, final_prediction, all_predictions, 
                                  confidence, comprehensive.method_weights)
        
        print("\n" + "=" * 80)
        print("🎉 综合预测完成！")
        print("基于概率排序、时间序列、马尔可夫链等多种科学方法")
        print("祝您好运！🍀")
        print("=" * 80)
        
        return {
            'period': next_period,
            'prediction': final_prediction,
            'all_predictions': all_predictions,
            'confidence': confidence
        }
    
    else:
        print("❌ 数据不可用")
        return None

def save_comprehensive_results(period, prediction, all_predictions, confidence, weights):
    """保存综合预测结果"""
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    with open('comprehensive_final_prediction.txt', 'w', encoding='utf-8') as f:
        f.write(f"综合最终预测结果\n")
        f.write(f"预测时间: {timestamp}\n")
        f.write("=" * 80 + "\n\n")
        
        f.write(f"预测期号: {period}\n")
        f.write(f"最终红球: {prediction['red']}\n")
        f.write(f"最终蓝球: {prediction['blue']}\n\n")
        
        f.write(f"预测置信度:\n")
        f.write(f"红球置信度: {confidence['red']:.1f}%\n")
        f.write(f"蓝球置信度: {confidence['blue']:.1f}%\n")
        f.write(f"综合置信度: {confidence['overall']:.1f}%\n\n")
        
        f.write(f"各方法预测详情:\n")
        for method, pred in all_predictions.items():
            weight = weights.get(method, 0) * 100
            f.write(f"{method} (权重{weight:.0f}%): 红球{pred['red']} 蓝球{pred['blue']}\n")
        
        f.write(f"\n方法权重分配:\n")
        for method, weight in weights.items():
            f.write(f"{method}: {weight*100:.0f}%\n")
    
    print(f"\n💾 综合预测结果已保存到 comprehensive_final_prediction.txt")

if __name__ == "__main__":
    result = main()
    
    if result:
        print(f"\n🎊 最终预测第{result['period']}期: "
              f"红球{result['prediction']['red']} 蓝球{result['prediction']['blue']}")
        print(f"📊 综合置信度: {result['confidence']['overall']:.1f}%")
