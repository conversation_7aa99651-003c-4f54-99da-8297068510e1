#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级预测方法性能测试脚本
比较各种科学方法的预测准确率
"""

from lottery_predictor import LotteryPredictor
from advanced_predictor import AdvancedLotteryPredictor
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def comprehensive_performance_test():
    """综合性能测试"""
    
    print("=" * 80)
    print("🧪 高级预测方法性能测试")
    print("=" * 80)
    
    # 创建预测器
    base_predictor = LotteryPredictor()
    
    print("📊 正在加载历史数据...")
    if not base_predictor.load_data():
        print("❌ 数据加载失败！")
        return
    
    base_predictor.analyze_data()
    base_predictor.calculate_intervals()
    
    advanced_predictor = AdvancedLotteryPredictor(base_predictor)
    
    # 测试参数
    total_periods = len(base_predictor.ssqhistory_all)
    test_size = 100  # 测试最近100期
    start_period = total_periods - test_size - 1
    
    print(f"📈 测试范围: 第{start_period}期 到 第{total_periods-1}期")
    print(f"📊 测试样本: {test_size}期")
    
    # 定义测试方法
    methods = {
        '马尔可夫链(1阶)': lambda idx: advanced_predictor.predict_with_markov(idx, order=1),
        '马尔可夫链(2阶)': lambda idx: advanced_predictor.predict_with_markov(idx, order=2),
        '贝叶斯概率': lambda idx: advanced_predictor.predict_with_bayesian(idx),
        '模式聚类': lambda idx: advanced_predictor.predict_with_clustering(idx),
        '时间序列': lambda idx: advanced_predictor.predict_with_time_series(idx),
        '统计学方法': lambda idx: base_predictor.predict_numbers_statistical(idx),
        '机器学习': lambda idx: base_predictor.predict_numbers_ml(idx),
    }
    
    # 初始化结果记录
    results = {}
    for method_name in methods.keys():
        results[method_name] = {
            'red_hits': [],
            'blue_hits': [],
            'total_score': [],
            'execution_time': []
        }
    
    # 添加集成方法
    results['集成预测'] = {
        'red_hits': [],
        'blue_hits': [],
        'total_score': [],
        'execution_time': []
    }
    
    print(f"\n🔄 开始性能测试...")
    
    # 逐期测试
    for i, period_idx in enumerate(range(start_period, total_periods)):
        if i % 20 == 0:
            print(f"   进度: {i}/{test_size}")
        
        # 获取实际结果
        actual_row = base_predictor.ssqhistory_all.iloc[period_idx]
        actual_red = [int(actual_row[f'红球{j}']) for j in range(1, 7)]
        actual_blue = int(actual_row['蓝球'])
        
        # 测试各种方法
        for method_name, method_func in methods.items():
            try:
                start_time = datetime.now()
                pred_red, pred_blue = method_func(period_idx)
                end_time = datetime.now()
                
                execution_time = (end_time - start_time).total_seconds()
                
                # 计算命中数
                red_hits = len(set(pred_red) & set(actual_red))
                blue_hit = 1 if pred_blue == actual_blue else 0
                
                # 计算总分（红球每个1分，蓝球2分）
                total_score = red_hits + blue_hit * 2
                
                results[method_name]['red_hits'].append(red_hits)
                results[method_name]['blue_hits'].append(blue_hit)
                results[method_name]['total_score'].append(total_score)
                results[method_name]['execution_time'].append(execution_time)
                
            except Exception as e:
                print(f"⚠️ 方法 {method_name} 在第{period_idx}期测试失败: {e}")
                results[method_name]['red_hits'].append(0)
                results[method_name]['blue_hits'].append(0)
                results[method_name]['total_score'].append(0)
                results[method_name]['execution_time'].append(0)
        
        # 测试集成方法
        try:
            start_time = datetime.now()
            pred_red, pred_blue, _ = advanced_predictor.ensemble_predict(period_idx)
            end_time = datetime.now()
            
            execution_time = (end_time - start_time).total_seconds()
            
            red_hits = len(set(pred_red) & set(actual_red))
            blue_hit = 1 if pred_blue == actual_blue else 0
            total_score = red_hits + blue_hit * 2
            
            results['集成预测']['red_hits'].append(red_hits)
            results['集成预测']['blue_hits'].append(blue_hit)
            results['集成预测']['total_score'].append(total_score)
            results['集成预测']['execution_time'].append(execution_time)
            
        except Exception as e:
            print(f"⚠️ 集成方法在第{period_idx}期测试失败: {e}")
            results['集成预测']['red_hits'].append(0)
            results['集成预测']['blue_hits'].append(0)
            results['集成预测']['total_score'].append(0)
            results['集成预测']['execution_time'].append(0)
    
    # 计算统计结果
    print(f"\n📊 性能测试结果分析...")
    
    performance_summary = {}
    for method_name, data in results.items():
        if len(data['red_hits']) > 0:
            performance_summary[method_name] = {
                'avg_red_hits': np.mean(data['red_hits']),
                'std_red_hits': np.std(data['red_hits']),
                'blue_hit_rate': np.mean(data['blue_hits']) * 100,
                'avg_total_score': np.mean(data['total_score']),
                'avg_execution_time': np.mean(data['execution_time']),
                'max_red_hits': max(data['red_hits']),
                'red_hit_distribution': [data['red_hits'].count(i) for i in range(7)]
            }
    
    # 显示结果
    print("\n" + "=" * 80)
    print("🏆 各方法性能排名")
    print("=" * 80)
    
    # 按平均总分排序
    sorted_methods = sorted(performance_summary.items(), 
                          key=lambda x: x[1]['avg_total_score'], reverse=True)
    
    print(f"{'方法名称':<15} {'平均红球命中':<12} {'蓝球命中率':<10} {'平均总分':<8} {'执行时间(s)':<10}")
    print("-" * 80)
    
    for method_name, stats in sorted_methods:
        print(f"{method_name:<15} {stats['avg_red_hits']:<12.2f} "
              f"{stats['blue_hit_rate']:<10.1f}% {stats['avg_total_score']:<8.2f} "
              f"{stats['avg_execution_time']:<10.3f}")
    
    # 详细分析
    print("\n" + "=" * 80)
    print("📈 详细性能分析")
    print("=" * 80)
    
    best_method = sorted_methods[0][0]
    best_stats = sorted_methods[0][1]
    
    print(f"\n🥇 最佳方法: {best_method}")
    print(f"   平均红球命中: {best_stats['avg_red_hits']:.2f} ± {best_stats['std_red_hits']:.2f}")
    print(f"   蓝球命中率: {best_stats['blue_hit_rate']:.1f}%")
    print(f"   最高红球命中: {best_stats['max_red_hits']}个")
    print(f"   平均执行时间: {best_stats['avg_execution_time']:.3f}秒")
    
    # 红球命中分布
    print(f"\n🔴 {best_method} 红球命中分布:")
    for i, count in enumerate(best_stats['red_hit_distribution']):
        percentage = count / test_size * 100
        print(f"   命中{i}个: {count}次 ({percentage:.1f}%)")
    
    # 方法比较分析
    print(f"\n🔍 方法比较分析:")
    
    # 找出红球命中率最高的方法
    best_red_method = max(performance_summary.items(), 
                         key=lambda x: x[1]['avg_red_hits'])
    print(f"   红球预测最佳: {best_red_method[0]} "
          f"(平均{best_red_method[1]['avg_red_hits']:.2f}个)")
    
    # 找出蓝球命中率最高的方法
    best_blue_method = max(performance_summary.items(), 
                          key=lambda x: x[1]['blue_hit_rate'])
    print(f"   蓝球预测最佳: {best_blue_method[0]} "
          f"({best_blue_method[1]['blue_hit_rate']:.1f}%)")
    
    # 找出执行速度最快的方法
    fastest_method = min(performance_summary.items(), 
                        key=lambda x: x[1]['avg_execution_time'])
    print(f"   执行速度最快: {fastest_method[0]} "
          f"({fastest_method[1]['avg_execution_time']:.3f}秒)")
    
    # 生成可视化图表
    create_performance_charts(performance_summary, test_size)
    
    # 保存详细结果
    save_performance_results(performance_summary, results, test_size)
    
    return performance_summary

def create_performance_charts(performance_summary, test_size):
    """创建性能分析图表"""
    
    print(f"\n📊 正在生成性能分析图表...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    methods = list(performance_summary.keys())
    
    # 1. 平均红球命中数比较
    red_hits = [performance_summary[method]['avg_red_hits'] for method in methods]
    axes[0, 0].bar(range(len(methods)), red_hits, color='red', alpha=0.7)
    axes[0, 0].set_title('平均红球命中数比较')
    axes[0, 0].set_ylabel('平均命中数')
    axes[0, 0].set_xticks(range(len(methods)))
    axes[0, 0].set_xticklabels(methods, rotation=45, ha='right')
    
    # 2. 蓝球命中率比较
    blue_rates = [performance_summary[method]['blue_hit_rate'] for method in methods]
    axes[0, 1].bar(range(len(methods)), blue_rates, color='blue', alpha=0.7)
    axes[0, 1].set_title('蓝球命中率比较')
    axes[0, 1].set_ylabel('命中率 (%)')
    axes[0, 1].set_xticks(range(len(methods)))
    axes[0, 1].set_xticklabels(methods, rotation=45, ha='right')
    
    # 3. 平均总分比较
    total_scores = [performance_summary[method]['avg_total_score'] for method in methods]
    axes[1, 0].bar(range(len(methods)), total_scores, color='green', alpha=0.7)
    axes[1, 0].set_title('平均总分比较')
    axes[1, 0].set_ylabel('平均总分')
    axes[1, 0].set_xticks(range(len(methods)))
    axes[1, 0].set_xticklabels(methods, rotation=45, ha='right')
    
    # 4. 执行时间比较
    exec_times = [performance_summary[method]['avg_execution_time'] for method in methods]
    axes[1, 1].bar(range(len(methods)), exec_times, color='orange', alpha=0.7)
    axes[1, 1].set_title('平均执行时间比较')
    axes[1, 1].set_ylabel('执行时间 (秒)')
    axes[1, 1].set_xticks(range(len(methods)))
    axes[1, 1].set_xticklabels(methods, rotation=45, ha='right')
    
    plt.tight_layout()
    plt.savefig('performance_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 性能分析图表已保存为 performance_analysis.png")

def save_performance_results(performance_summary, detailed_results, test_size):
    """保存性能测试结果"""
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    with open('performance_test_results.txt', 'w', encoding='utf-8') as f:
        f.write(f"高级预测方法性能测试报告\n")
        f.write(f"测试时间: {timestamp}\n")
        f.write(f"测试样本: {test_size}期\n")
        f.write("=" * 80 + "\n\n")
        
        # 性能排名
        sorted_methods = sorted(performance_summary.items(), 
                              key=lambda x: x[1]['avg_total_score'], reverse=True)
        
        f.write("性能排名 (按平均总分):\n")
        f.write("-" * 80 + "\n")
        for i, (method, stats) in enumerate(sorted_methods, 1):
            f.write(f"{i}. {method}\n")
            f.write(f"   平均红球命中: {stats['avg_red_hits']:.2f} ± {stats['std_red_hits']:.2f}\n")
            f.write(f"   蓝球命中率: {stats['blue_hit_rate']:.1f}%\n")
            f.write(f"   平均总分: {stats['avg_total_score']:.2f}\n")
            f.write(f"   平均执行时间: {stats['avg_execution_time']:.3f}秒\n")
            f.write(f"   最高红球命中: {stats['max_red_hits']}个\n\n")
        
        # 详细统计
        f.write("\n详细统计分析:\n")
        f.write("-" * 80 + "\n")
        
        best_method = sorted_methods[0][0]
        f.write(f"综合表现最佳: {best_method}\n")
        
        best_red = max(performance_summary.items(), key=lambda x: x[1]['avg_red_hits'])
        f.write(f"红球预测最佳: {best_red[0]} (平均{best_red[1]['avg_red_hits']:.2f}个)\n")
        
        best_blue = max(performance_summary.items(), key=lambda x: x[1]['blue_hit_rate'])
        f.write(f"蓝球预测最佳: {best_blue[0]} ({best_blue[1]['blue_hit_rate']:.1f}%)\n")
        
        fastest = min(performance_summary.items(), key=lambda x: x[1]['avg_execution_time'])
        f.write(f"执行速度最快: {fastest[0]} ({fastest[1]['avg_execution_time']:.3f}秒)\n")
    
    print(f"💾 详细测试结果已保存到 performance_test_results.txt")

if __name__ == "__main__":
    performance_summary = comprehensive_performance_test()
    
    if performance_summary:
        print(f"\n🎉 性能测试完成！")
        print(f"📊 共测试了{len(performance_summary)}种预测方法")
        
        best_method = max(performance_summary.items(), 
                         key=lambda x: x[1]['avg_total_score'])
        print(f"🏆 综合表现最佳: {best_method[0]} "
              f"(平均总分: {best_method[1]['avg_total_score']:.2f})")
