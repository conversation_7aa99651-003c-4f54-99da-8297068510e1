#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级双色球预测脚本
集成多种科学方法：贝叶斯概率、马尔可夫链、时间序列分析等
"""

from lottery_predictor import LotteryPredictor
from advanced_predictor import AdvancedLotteryPredictor
import numpy as np
import pandas as pd
from datetime import datetime

def advanced_prediction_analysis():
    """高级预测分析"""
    
    print("=" * 80)
    print("🧠 高级双色球智能预测系统 🧠")
    print("集成贝叶斯概率、马尔可夫链、时间序列分析等科学方法")
    print("=" * 80)
    
    # 创建基础预测器
    base_predictor = LotteryPredictor()
    
    print("📊 正在加载历史数据...")
    if not base_predictor.load_data():
        print("❌ 数据加载失败！")
        return
    
    print("✅ 数据加载成功！")
    base_predictor.analyze_data()
    base_predictor.calculate_intervals()
    
    # 创建高级预测器
    advanced_predictor = AdvancedLotteryPredictor(base_predictor)
    
    # 获取最新期号信息
    latest_row = base_predictor.ssqhistory_all.iloc[-1]
    latest_period = int(latest_row['期号'])
    latest_red = [int(latest_row[f'红球{i}']) for i in range(1, 7)]
    latest_blue = int(latest_row['蓝球'])
    
    print(f"\n📅 最新开奖期号: {latest_period}")
    print(f"🔴 最新红球号码: {latest_red}")
    print(f"🔵 最新蓝球号码: {latest_blue}")
    
    current_period_index = len(base_predictor.ssqhistory_all)
    next_period = latest_period + 1
    
    print(f"\n🔬 正在进行深度分析...")
    
    # 1. 相关性分析
    print("\n🔍 相关性分析...")
    correlations = advanced_predictor.correlation_analysis(lag_periods=10)
    
    print("红球自相关性:")
    for lag, corr in correlations['red_autocorr'][:5]:
        print(f"   滞后{lag}期: {corr:.4f}")
    
    print("蓝球自相关性:")
    for lag, corr in correlations['blue_autocorr'][:5]:
        print(f"   滞后{lag}期: {corr:.4f}")
    
    print("红蓝球交叉相关性:")
    for lag, corr in correlations['cross_correlations'][:5]:
        print(f"   滞后{lag}期: {corr:.4f}")
    
    # 2. 时间序列模式分析
    print("\n📈 时间序列模式分析...")
    patterns = advanced_predictor.analyze_temporal_patterns(window_size=15)
    
    if patterns['red_trends']:
        avg_red_trend = np.mean(patterns['red_trends'][-10:])
        print(f"最近红球趋势: {avg_red_trend:.4f}")
    
    if patterns['blue_trends']:
        avg_blue_trend = np.mean(patterns['blue_trends'][-10:])
        print(f"最近蓝球趋势: {avg_blue_trend:.4f}")
    
    # 3. 模式聚类分析
    print("\n🎯 模式聚类分析...")
    cluster_info = advanced_predictor.pattern_clustering(n_clusters=8)
    
    print("聚类分布:")
    for cluster_id, info in cluster_info.items():
        print(f"   聚类{cluster_id}: {info['size']}期 ({info['percentage']:.1f}%)")
        if info['recent_periods']:
            print(f"     最近出现: {info['recent_periods']}")
    
    # 4. 马尔可夫链分析
    print("\n🔗 马尔可夫链分析...")
    markov_1 = advanced_predictor.build_markov_chain(order=1)
    markov_2 = advanced_predictor.build_markov_chain(order=2)
    
    print("✅ 1阶和2阶马尔可夫链构建完成")
    
    # 5. 各种方法预测
    print(f"\n🎲 正在使用多种科学方法预测第 {next_period} 期...")
    
    methods_results = {}
    
    # 马尔可夫链预测
    print("   🔗 马尔可夫链方法...")
    red_markov1, blue_markov1 = advanced_predictor.predict_with_markov(current_period_index, order=1)
    red_markov2, blue_markov2 = advanced_predictor.predict_with_markov(current_period_index, order=2)
    
    methods_results['马尔可夫链(1阶)'] = {'red': red_markov1, 'blue': blue_markov1}
    methods_results['马尔可夫链(2阶)'] = {'red': red_markov2, 'blue': blue_markov2}
    
    # 贝叶斯预测
    print("   🎯 贝叶斯概率方法...")
    red_bayes, blue_bayes = advanced_predictor.predict_with_bayesian(current_period_index)
    methods_results['贝叶斯概率'] = {'red': red_bayes, 'blue': blue_bayes}
    
    # 聚类预测
    print("   🎯 模式聚类方法...")
    red_cluster, blue_cluster = advanced_predictor.predict_with_clustering(current_period_index)
    methods_results['模式聚类'] = {'red': red_cluster, 'blue': blue_cluster}
    
    # 时间序列预测
    print("   📈 时间序列方法...")
    red_ts, blue_ts = advanced_predictor.predict_with_time_series(current_period_index)
    methods_results['时间序列'] = {'red': red_ts, 'blue': blue_ts}
    
    # 传统方法
    print("   📊 传统统计方法...")
    red_stat, blue_stat = base_predictor.predict_numbers_statistical(current_period_index)
    methods_results['统计学方法'] = {'red': red_stat, 'blue': blue_stat}
    
    print("   🤖 机器学习方法...")
    red_ml, blue_ml = base_predictor.predict_numbers_ml(current_period_index)
    methods_results['机器学习'] = {'red': red_ml, 'blue': blue_ml}
    
    # 集成预测
    print("   🎯 集成预测方法...")
    red_ensemble, blue_ensemble, all_preds = advanced_predictor.ensemble_predict(current_period_index)
    methods_results['集成预测'] = {'red': red_ensemble, 'blue': blue_ensemble}
    
    # 显示所有预测结果
    print("\n" + "=" * 80)
    print("🎯 各种科学方法预测结果")
    print("=" * 80)
    
    for method_name, pred in methods_results.items():
        print(f"\n📊 {method_name}:")
        print(f"   🔴 红球: {pred['red']}")
        print(f"   🔵 蓝球: {pred['blue']}")
    
    # 分析预测一致性
    print("\n" + "=" * 80)
    print("🔍 预测一致性分析")
    print("=" * 80)
    
    # 红球一致性分析
    all_red_predictions = []
    for pred in methods_results.values():
        all_red_predictions.extend(pred['red'])
    
    red_consensus = {}
    for ball in all_red_predictions:
        red_consensus[ball] = red_consensus.get(ball, 0) + 1
    
    print("\n🔴 红球号码支持度:")
    sorted_red_consensus = sorted(red_consensus.items(), key=lambda x: x[1], reverse=True)
    for ball, count in sorted_red_consensus[:10]:
        support_rate = count / len(methods_results) * 100
        print(f"   {ball:2d}号: {count}个方法支持 ({support_rate:.1f}%)")
    
    # 蓝球一致性分析
    blue_predictions = [pred['blue'] for pred in methods_results.values()]
    blue_consensus = {}
    for ball in blue_predictions:
        blue_consensus[ball] = blue_consensus.get(ball, 0) + 1
    
    print("\n🔵 蓝球号码支持度:")
    sorted_blue_consensus = sorted(blue_consensus.items(), key=lambda x: x[1], reverse=True)
    for ball, count in sorted_blue_consensus[:5]:
        support_rate = count / len(methods_results) * 100
        print(f"   {ball:2d}号: {count}个方法支持 ({support_rate:.1f}%)")
    
    # 最终推荐
    print("\n" + "=" * 80)
    print("🏆 最终推荐号码")
    print("=" * 80)
    
    # 高支持度红球
    high_support_red = [ball for ball, count in sorted_red_consensus if count >= 3][:6]
    if len(high_support_red) < 6:
        # 补充集成预测的号码
        for ball in red_ensemble:
            if ball not in high_support_red and len(high_support_red) < 6:
                high_support_red.append(ball)
    
    # 如果还不够，随机补充
    if len(high_support_red) < 6:
        available = [i for i in range(1, 34) if i not in high_support_red]
        needed = 6 - len(high_support_red)
        high_support_red.extend(np.random.choice(available, needed, replace=False))
    
    final_red = sorted(high_support_red[:6])
    
    # 高支持度蓝球
    final_blue = sorted_blue_consensus[0][0] if sorted_blue_consensus else blue_ensemble
    
    print(f"\n🎯 第 {next_period} 期推荐号码:")
    print(f"🔴 红球: {final_red}")
    print(f"🔵 蓝球: {final_blue}")
    
    # 置信度分析
    red_confidence = sum(count for ball, count in sorted_red_consensus[:6]) / (len(methods_results) * 6) * 100
    blue_confidence = sorted_blue_consensus[0][1] / len(methods_results) * 100 if sorted_blue_consensus else 0
    
    print(f"\n📊 预测置信度:")
    print(f"   红球平均置信度: {red_confidence:.1f}%")
    print(f"   蓝球置信度: {blue_confidence:.1f}%")
    
    # 保存详细预测结果
    save_advanced_prediction(next_period, final_red, final_blue, methods_results, 
                           correlations, patterns, cluster_info)
    
    # 风险提示
    print("\n" + "=" * 80)
    print("⚠️  科学预测风险提示")
    print("=" * 80)
    print("• 虽然使用了多种科学方法，但彩票本质上仍具有随机性")
    print("• 相关性分析和模式识别可能发现伪规律")
    print("• 历史数据不能完全预测未来结果")
    print("• 请理性购彩，切勿过度投注")
    
    return {
        'period': next_period,
        'final_red': final_red,
        'final_blue': final_blue,
        'methods_results': methods_results,
        'confidence': {'red': red_confidence, 'blue': blue_confidence}
    }

def save_advanced_prediction(period, red_balls, blue_ball, methods_results, 
                           correlations, patterns, cluster_info):
    """保存高级预测结果"""
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 保存到详细记录文件
    with open('advanced_predictions.txt', 'a', encoding='utf-8') as f:
        f.write(f"\n{'='*80}\n")
        f.write(f"高级预测记录 - {timestamp}\n")
        f.write(f"{'='*80}\n")
        f.write(f"预测期号: {period}\n")
        f.write(f"推荐红球: {red_balls}\n")
        f.write(f"推荐蓝球: {blue_ball}\n\n")
        
        f.write("各方法预测结果:\n")
        for method, pred in methods_results.items():
            f.write(f"  {method}: 红球{pred['red']} 蓝球{pred['blue']}\n")
        
        f.write(f"\n相关性分析:\n")
        f.write(f"  红球自相关: {correlations['red_autocorr'][:3]}\n")
        f.write(f"  蓝球自相关: {correlations['blue_autocorr'][:3]}\n")
        
        f.write(f"\n聚类分析:\n")
        for cluster_id, info in list(cluster_info.items())[:3]:
            f.write(f"  聚类{cluster_id}: {info['size']}期 ({info['percentage']:.1f}%)\n")
    
    print(f"\n💾 详细预测结果已保存到 advanced_predictions.txt")

if __name__ == "__main__":
    result = advanced_prediction_analysis()
    
    if result:
        print(f"\n🎉 高级科学预测完成！期待第{result['period']}期开奖结果！ 🍀")
