#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
双色球号码预测系统
基于历史数据的统计学分析和机器学习方法
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class LotteryPredictor:
    """双色球预测系统"""
    
    def __init__(self, excel_file='lottery_data_all.xlsx', sheet_name='SSQ_data_all'):
        """
        初始化预测系统
        
        Args:
            excel_file: Excel文件路径
            sheet_name: 工作表名称
        """
        self.excel_file = excel_file
        self.sheet_name = sheet_name
        self.ssqhistory_all = None
        self.red_ball_stats = {}
        self.blue_ball_stats = {}
        
    def load_data(self):
        """
        读取Excel文件中的双色球历史数据
        读取A列（期号）和I-O列（红球1-6，蓝球）数据
        """
        try:
            # 读取Excel文件
            print(f"正在读取文件: {self.excel_file}")
            df = pd.read_excel(self.excel_file, sheet_name=self.sheet_name)
            
            # 显示原始数据信息
            print(f"原始数据形状: {df.shape}")
            print(f"列名: {list(df.columns)}")
            
            # 选择A列（期号）和I-O列（红球1-6，蓝球）
            # A列为期号，I-N列为红球（6个），O列为蓝球
            columns_to_select = [df.columns[0]]  # A列（期号）
            columns_to_select.extend(df.columns[8:15])  # I-O列（红球1-6，蓝球）
            
            print(f"选择的列: {columns_to_select}")
            
            # 提取指定列的数据
            self.ssqhistory_all = df[columns_to_select].copy()
            
            # 重命名列名以便于理解
            new_columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '红球6', '蓝球']
            self.ssqhistory_all.columns = new_columns
            
            # 删除包含空值的行
            self.ssqhistory_all = self.ssqhistory_all.dropna()
            
            # 按期号排序（从小到大）
            self.ssqhistory_all = self.ssqhistory_all.sort_values('期号').reset_index(drop=True)
            
            print(f"处理后数据形状: {self.ssqhistory_all.shape}")
            print("数据读取完成！")
            print("\n前5行数据:")
            print(self.ssqhistory_all.head())
            print("\n后5行数据:")
            print(self.ssqhistory_all.tail())
            
            return True
            
        except Exception as e:
            print(f"数据读取失败: {str(e)}")
            return False
    
    def analyze_data(self):
        """分析历史数据的统计特性"""
        if self.ssqhistory_all is None:
            print("请先加载数据！")
            return
        
        print("\n=== 数据统计分析 ===")
        
        # 红球统计分析
        red_balls = self.ssqhistory_all[['红球1', '红球2', '红球3', '红球4', '红球5', '红球6']]
        blue_balls = self.ssqhistory_all['蓝球']
        
        # 红球号码出现频率统计
        red_frequency = {}
        for i in range(1, 34):  # 红球范围1-33
            count = (red_balls == i).sum().sum()
            red_frequency[i] = count
        
        # 蓝球号码出现频率统计
        blue_frequency = {}
        for i in range(1, 17):  # 蓝球范围1-16
            count = (blue_balls == i).sum()
            blue_frequency[i] = count
        
        self.red_ball_stats['frequency'] = red_frequency
        self.blue_ball_stats['frequency'] = blue_frequency
        
        print(f"总期数: {len(self.ssqhistory_all)}")
        print(f"红球出现频率最高的5个号码: {sorted(red_frequency.items(), key=lambda x: x[1], reverse=True)[:5]}")
        print(f"蓝球出现频率最高的5个号码: {sorted(blue_frequency.items(), key=lambda x: x[1], reverse=True)[:5]}")
        
        # 计算红球和蓝球的统计特征
        red_mean = red_balls.values.flatten().mean()
        red_std = red_balls.values.flatten().std()
        blue_mean = blue_balls.mean()
        blue_std = blue_balls.std()
        
        print(f"\n红球统计特征:")
        print(f"  平均值: {red_mean:.2f}")
        print(f"  标准差: {red_std:.2f}")
        print(f"蓝球统计特征:")
        print(f"  平均值: {blue_mean:.2f}")
        print(f"  标准差: {blue_std:.2f}")
        
        self.red_ball_stats.update({
            'mean': red_mean,
            'std': red_std,
            'min': 1,
            'max': 33
        })
        
        self.blue_ball_stats.update({
            'mean': blue_mean,
            'std': blue_std,
            'min': 1,
            'max': 16
        })
    
    def calculate_intervals(self):
        """计算号码间隔统计"""
        if self.ssqhistory_all is None:
            return
        
        print("\n=== 号码间隔分析 ===")
        
        # 计算每个红球号码的间隔
        red_intervals = {}
        for ball_num in range(1, 34):
            intervals = []
            last_appear = -1
            
            for idx, row in self.ssqhistory_all.iterrows():
                red_balls = [row['红球1'], row['红球2'], row['红球3'], 
                           row['红球4'], row['红球5'], row['红球6']]
                
                if ball_num in red_balls:
                    if last_appear != -1:
                        intervals.append(idx - last_appear)
                    last_appear = idx
            
            if intervals:
                red_intervals[ball_num] = {
                    'avg_interval': np.mean(intervals),
                    'std_interval': np.std(intervals),
                    'intervals': intervals
                }
        
        self.red_ball_stats['intervals'] = red_intervals
        
        # 显示间隔统计
        avg_intervals = [(k, v['avg_interval']) for k, v in red_intervals.items()]
        avg_intervals.sort(key=lambda x: x[1])
        print(f"红球平均间隔最短的5个号码: {avg_intervals[:5]}")
        print(f"红球平均间隔最长的5个号码: {avg_intervals[-5:]}")

    def extract_features(self, period_index, lookback_periods=50):
        """
        提取特征用于预测

        Args:
            period_index: 当前期数索引
            lookback_periods: 回看期数

        Returns:
            features: 特征向量
        """
        if self.ssqhistory_all is None:
            return np.zeros(50)

        if period_index < lookback_periods:
            lookback_periods = period_index

        if lookback_periods == 0:
            return np.zeros(50)  # 返回零向量作为默认特征

        # 获取历史数据
        history_data = self.ssqhistory_all.iloc[period_index-lookback_periods:period_index]

        features = []

        # 1. 红球频率特征（最近N期每个号码出现次数）
        red_freq = np.zeros(33)
        for _, row in history_data.iterrows():
            for i in range(1, 7):
                ball = int(row[f'红球{i}'])
                red_freq[ball-1] += 1
        features.extend(red_freq)

        # 2. 蓝球频率特征
        blue_freq = np.zeros(16)
        for _, row in history_data.iterrows():
            ball = int(row['蓝球'])
            blue_freq[ball-1] += 1
        features.extend(blue_freq)

        # 3. 最近一期的号码作为特征
        if len(history_data) > 0:
            last_row = history_data.iloc[-1]
            last_red = [int(last_row[f'红球{i}']) for i in range(1, 7)]
            last_blue = int(last_row['蓝球'])
            features.extend(last_red)
            features.append(last_blue)
        else:
            features.extend([0] * 7)

        return np.array(features)

    def predict_numbers_statistical(self, period_index, lookback_periods=100):
        """
        基于统计学方法预测号码

        Args:
            period_index: 当前期数索引
            lookback_periods: 回看期数

        Returns:
            predicted_red: 预测的6个红球号码
            predicted_blue: 预测的蓝球号码
        """
        if self.ssqhistory_all is None:
            predicted_red = sorted(np.random.choice(range(1, 34), 6, replace=False))
            predicted_blue = np.random.choice(range(1, 17))
            return predicted_red, predicted_blue

        if period_index < lookback_periods:
            lookback_periods = period_index

        if lookback_periods == 0:
            # 如果没有历史数据，随机选择
            predicted_red = sorted(np.random.choice(range(1, 34), 6, replace=False))
            predicted_blue = np.random.choice(range(1, 17))
            return predicted_red, predicted_blue

        # 获取历史数据
        history_data = self.ssqhistory_all.iloc[period_index-lookback_periods:period_index]

        # 方法1: 基于频率和正态分布的预测
        red_weights = np.zeros(33)
        blue_weights = np.zeros(16)

        # 计算权重（越近期的数据权重越大）
        for i, (_, row) in enumerate(history_data.iterrows()):
            weight = (i + 1) / len(history_data)  # 线性递增权重

            # 红球权重
            for j in range(1, 7):
                ball = int(row[f'红球{j}'])
                red_weights[ball-1] += weight

            # 蓝球权重
            ball = int(row['蓝球'])
            blue_weights[ball-1] += weight

        # 方法2: 考虑间隔因素
        # 计算每个号码距离上次出现的间隔
        red_intervals = np.full(33, lookback_periods)  # 默认最大间隔
        blue_intervals = np.full(16, lookback_periods)

        for i in range(len(history_data)-1, -1, -1):
            row = history_data.iloc[i]
            interval = len(history_data) - 1 - i

            # 红球间隔
            for j in range(1, 7):
                ball = int(row[f'红球{j}'])
                if red_intervals[ball-1] == lookback_periods:  # 还没有记录间隔
                    red_intervals[ball-1] = interval

            # 蓝球间隔
            ball = int(row['蓝球'])
            if blue_intervals[ball-1] == lookback_periods:
                blue_intervals[ball-1] = interval

        # 方法3: 正态分布调整
        # 基于历史平均值和标准差调整权重
        red_mean = self.red_ball_stats.get('mean', 17)
        red_std = self.red_ball_stats.get('std', 9.5)

        for i in range(33):
            ball_num = i + 1
            # 计算该号码与平均值的距离，距离越近权重越高
            distance = abs(ball_num - red_mean) / red_std
            normal_weight = stats.norm.pdf(distance)
            red_weights[i] *= (1 + normal_weight)

        # 方法4: 间隔权重调整
        # 间隔越长的号码，出现概率越高
        max_interval = max(red_intervals)
        for i in range(33):
            interval_weight = red_intervals[i] / max_interval
            red_weights[i] *= (1 + interval_weight)

        max_blue_interval = max(blue_intervals)
        for i in range(16):
            interval_weight = blue_intervals[i] / max_blue_interval
            blue_weights[i] *= (1 + interval_weight)

        # 选择红球（选择权重最高的6个，但加入随机性）
        # 使用加权随机选择而不是直接选择最高权重
        red_probs = red_weights / red_weights.sum()
        predicted_red = []
        available_balls = list(range(1, 34))
        available_probs = red_probs.copy()

        for _ in range(6):
            # 加权随机选择
            choice_idx = np.random.choice(len(available_balls), p=available_probs/available_probs.sum())
            chosen_ball = available_balls[choice_idx]
            predicted_red.append(chosen_ball)

            # 移除已选择的球
            available_balls.pop(choice_idx)
            available_probs = np.delete(available_probs, choice_idx)

        predicted_red = sorted(predicted_red)

        # 选择蓝球
        blue_probs = blue_weights / blue_weights.sum()
        predicted_blue = np.random.choice(range(1, 17), p=blue_probs)

        return predicted_red, predicted_blue

    def predict_numbers_ml(self, period_index, lookback_periods=200):
        """
        基于机器学习方法预测号码

        Args:
            period_index: 当前期数索引
            lookback_periods: 回看期数

        Returns:
            predicted_red: 预测的6个红球号码
            predicted_blue: 预测的蓝球号码
        """
        if self.ssqhistory_all is None or period_index < 50:
            # 数据不足时使用统计方法
            return self.predict_numbers_statistical(period_index, lookback_periods)

        # 准备训练数据
        X = []
        y_red = []
        y_blue = []

        start_idx = max(50, period_index - lookback_periods)
        for i in range(start_idx, period_index):
            features = self.extract_features(i, 30)
            X.append(features)

            # 目标值
            row = self.ssqhistory_all.iloc[i]
            red_balls = [int(row[f'红球{j}']) for j in range(1, 7)]
            blue_ball = int(row['蓝球'])

            y_red.append(red_balls)
            y_blue.append(blue_ball)

        if len(X) < 10:
            return self.predict_numbers_statistical(period_index, lookback_periods)

        X = np.array(X)
        y_red = np.array(y_red)
        y_blue = np.array(y_blue)

        # 训练模型预测红球
        predicted_red = []
        for ball_pos in range(6):
            rf = RandomForestRegressor(n_estimators=50, random_state=42)
            rf.fit(X, y_red[:, ball_pos])

            # 预测当前期
            current_features = self.extract_features(period_index, 30).reshape(1, -1)
            pred_value = rf.predict(current_features)[0]
            predicted_red.append(max(1, min(33, int(round(pred_value)))))

        # 去重并补充
        predicted_red = list(set(predicted_red))
        while len(predicted_red) < 6:
            # 随机添加未选中的号码
            available = [i for i in range(1, 34) if i not in predicted_red]
            if available:
                predicted_red.append(np.random.choice(available))
            else:
                break

        predicted_red = sorted(predicted_red[:6])

        # 训练模型预测蓝球
        rf_blue = RandomForestRegressor(n_estimators=50, random_state=42)
        rf_blue.fit(X, y_blue)
        current_features = self.extract_features(period_index, 30).reshape(1, -1)
        predicted_blue = max(1, min(16, int(round(rf_blue.predict(current_features)[0]))))

        return predicted_red, predicted_blue

    def calculate_accuracy(self, predicted_red, predicted_blue, actual_red, actual_blue):
        """
        计算预测准确性

        Args:
            predicted_red: 预测红球
            predicted_blue: 预测蓝球
            actual_red: 实际红球
            actual_blue: 实际蓝球

        Returns:
            red_matches: 红球命中数
            blue_match: 蓝球是否命中
        """
        red_matches = len(set(predicted_red) & set(actual_red))
        blue_match = predicted_blue == actual_blue
        return red_matches, blue_match

    def backtest(self, start_period=100, end_period=None, method='statistical'):
        """
        回归测试

        Args:
            start_period: 开始测试的期数
            end_period: 结束测试的期数
            method: 预测方法 ('statistical' 或 'ml')

        Returns:
            results: 测试结果统计
        """
        if self.ssqhistory_all is None:
            print("请先加载数据！")
            return None

        if end_period is None:
            end_period = len(self.ssqhistory_all) - 1

        results = {
            'total_tests': 0,
            'red_matches': [0, 0, 0, 0, 0, 0, 0],  # 0-6个红球命中的次数
            'blue_matches': 0,
            'predictions': []
        }

        print(f"\n=== 回归测试 ({method}方法) ===")
        print(f"测试期数: {start_period} - {end_period}")

        for period in range(start_period, end_period + 1):
            if method == 'statistical':
                pred_red, pred_blue = self.predict_numbers_statistical(period)
            else:
                pred_red, pred_blue = self.predict_numbers_ml(period)

            # 获取实际结果
            actual_row = self.ssqhistory_all.iloc[period]
            actual_red = [int(actual_row[f'红球{i}']) for i in range(1, 7)]
            actual_blue = int(actual_row['蓝球'])

            # 计算准确性
            red_hits, blue_hit = self.calculate_accuracy(pred_red, pred_blue, actual_red, actual_blue)

            results['total_tests'] += 1
            results['red_matches'][red_hits] += 1
            if blue_hit:
                results['blue_matches'] += 1

            results['predictions'].append({
                'period': int(actual_row['期号']),
                'predicted_red': pred_red,
                'predicted_blue': pred_blue,
                'actual_red': actual_red,
                'actual_blue': actual_blue,
                'red_hits': red_hits,
                'blue_hit': blue_hit
            })

            # 显示进度
            if period % 100 == 0:
                print(f"已测试到第 {period} 期...")

        # 计算统计结果
        total = results['total_tests']
        print(f"\n=== 测试结果统计 ===")
        print(f"总测试期数: {total}")
        print(f"红球命中统计:")
        for i in range(7):
            count = results['red_matches'][i]
            percentage = count / total * 100
            print(f"  命中{i}个红球: {count}次 ({percentage:.2f}%)")

        blue_percentage = results['blue_matches'] / total * 100
        print(f"蓝球命中: {results['blue_matches']}次 ({blue_percentage:.2f}%)")

        # 计算期望收益（简化计算）
        expected_return = self.calculate_expected_return(results)
        print(f"期望收益率: {expected_return:.2f}%")

        return results

    def calculate_expected_return(self, results):
        """
        计算期望收益率（简化版本）
        """
        # 双色球奖金等级（简化）
        prize_levels = {
            (6, True): 5000000,   # 一等奖
            (6, False): 200000,   # 二等奖
            (5, True): 3000,      # 三等奖
            (5, False): 200,      # 四等奖
            (4, True): 200,       # 五等奖
            (4, False): 10,       # 六等奖
            (3, True): 10,        # 六等奖
            (2, True): 5,         # 六等奖
            (1, True): 5,         # 六等奖
            (0, True): 5          # 六等奖
        }

        total_investment = results['total_tests'] * 2  # 每注2元
        total_winnings = 0

        for pred in results['predictions']:
            red_hits = pred['red_hits']
            blue_hit = pred['blue_hit']

            # 查找对应奖金
            for (r, b), prize in prize_levels.items():
                if red_hits >= r and (not b or blue_hit):
                    total_winnings += prize
                    break

        return (total_winnings - total_investment) / total_investment * 100

if __name__ == "__main__":
    # 创建预测器实例
    predictor = LotteryPredictor()

    # 加载数据
    if predictor.load_data():
        # 分析数据
        predictor.analyze_data()
        predictor.calculate_intervals()

        # 测试预测功能
        if predictor.ssqhistory_all is not None:
            print("\n=== 预测测试 ===")
            test_period = len(predictor.ssqhistory_all) - 1

            # 统计学方法预测
            red_pred_stat, blue_pred_stat = predictor.predict_numbers_statistical(test_period)
            print(f"基于统计学方法预测的号码:")
            print(f"红球: {red_pred_stat}")
            print(f"蓝球: {blue_pred_stat}")

            # 机器学习方法预测
            red_pred_ml, blue_pred_ml = predictor.predict_numbers_ml(test_period)
            print(f"基于机器学习方法预测的号码:")
            print(f"红球: {red_pred_ml}")
            print(f"蓝球: {blue_pred_ml}")

            # 显示实际号码进行对比
            actual_row = predictor.ssqhistory_all.iloc[test_period]
            actual_red = [int(actual_row[f'红球{i}']) for i in range(1, 7)]
            actual_blue = int(actual_row['蓝球'])
            print(f"实际号码:")
            print(f"红球: {actual_red}")
            print(f"蓝球: {actual_blue}")

            # 计算命中情况
            red_hits_stat, blue_hit_stat = predictor.calculate_accuracy(red_pred_stat, blue_pred_stat, actual_red, actual_blue)
            red_hits_ml, blue_hit_ml = predictor.calculate_accuracy(red_pred_ml, blue_pred_ml, actual_red, actual_blue)
            print(f"\n命中情况:")
            print(f"统计学方法: 红球{red_hits_stat}个, 蓝球{'✓' if blue_hit_stat else '✗'}")
            print(f"机器学习方法: 红球{red_hits_ml}个, 蓝球{'✓' if blue_hit_ml else '✗'}")

            # 进行小规模回归测试
            print("\n=== 小规模回归测试 ===")
            total_periods = len(predictor.ssqhistory_all)
            test_start = max(200, total_periods - 100)  # 测试最近100期

            print("正在进行统计学方法回归测试...")
            results_stat = predictor.backtest(test_start, total_periods - 1, 'statistical')

            print("\n正在进行机器学习方法回归测试...")
            results_ml = predictor.backtest(test_start, total_periods - 1, 'ml')

            # 比较两种方法
            if results_stat and results_ml:
                print("\n=== 方法比较 ===")
                stat_avg_red = sum(i * results_stat['red_matches'][i] for i in range(7)) / results_stat['total_tests']
                ml_avg_red = sum(i * results_ml['red_matches'][i] for i in range(7)) / results_ml['total_tests']

                stat_blue_rate = results_stat['blue_matches'] / results_stat['total_tests'] * 100
                ml_blue_rate = results_ml['blue_matches'] / results_ml['total_tests'] * 100

                print(f"统计学方法 - 平均红球命中: {stat_avg_red:.2f}个, 蓝球命中率: {stat_blue_rate:.2f}%")
                print(f"机器学习方法 - 平均红球命中: {ml_avg_red:.2f}个, 蓝球命中率: {ml_blue_rate:.2f}%")

                if ml_avg_red > stat_avg_red:
                    print("机器学习方法在红球预测上表现更好")
                else:
                    print("统计学方法在红球预测上表现更好")

                if ml_blue_rate > stat_blue_rate:
                    print("机器学习方法在蓝球预测上表现更好")
                else:
                    print("统计学方法在蓝球预测上表现更好")
    else:
        print("数据加载失败，请检查文件路径和格式！")
