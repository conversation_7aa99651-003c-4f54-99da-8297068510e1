#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级双色球预测模块
实现贝叶斯概率、马尔可夫链、时间序列分析等高级统计学方法
"""

import numpy as np
import pandas as pd
from scipy import stats
from scipy.special import gamma
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
import warnings
warnings.filterwarnings('ignore')

class AdvancedLotteryPredictor:
    """高级双色球预测器"""
    
    def __init__(self, base_predictor):
        """
        初始化高级预测器
        
        Args:
            base_predictor: 基础预测器实例
        """
        self.base_predictor = base_predictor
        self.data = base_predictor.ssqhistory_all
        self.transition_matrices = {}
        self.bayesian_priors = {}
        self.pattern_clusters = {}
        
    def build_markov_chain(self, order=1):
        """
        构建马尔可夫链模型
        
        Args:
            order: 马尔可夫链的阶数
        
        Returns:
            transition_matrices: 转移概率矩阵
        """
        print(f"🔗 构建{order}阶马尔可夫链...")
        
        # 红球马尔可夫链
        red_states = list(range(1, 34))  # 红球状态空间
        red_transition = np.zeros((33, 33))
        
        # 统计状态转移
        for i in range(order, len(self.data)):
            current_row = self.data.iloc[i]
            prev_row = self.data.iloc[i-order]
            
            current_red = [int(current_row[f'红球{j}']) for j in range(1, 7)]
            prev_red = [int(prev_row[f'红球{j}']) for j in range(1, 7)]
            
            # 计算每个红球的转移
            for prev_ball in prev_red:
                for curr_ball in current_red:
                    red_transition[prev_ball-1][curr_ball-1] += 1
        
        # 归一化为概率
        for i in range(33):
            row_sum = red_transition[i].sum()
            if row_sum > 0:
                red_transition[i] = red_transition[i] / row_sum
        
        # 蓝球马尔可夫链
        blue_transition = np.zeros((16, 16))
        for i in range(order, len(self.data)):
            current_blue = int(self.data.iloc[i]['蓝球'])
            prev_blue = int(self.data.iloc[i-order]['蓝球'])
            blue_transition[prev_blue-1][current_blue-1] += 1
        
        # 归一化
        for i in range(16):
            row_sum = blue_transition[i].sum()
            if row_sum > 0:
                blue_transition[i] = blue_transition[i] / row_sum
        
        self.transition_matrices[order] = {
            'red': red_transition,
            'blue': blue_transition
        }
        
        return self.transition_matrices[order]
    
    def bayesian_update(self, prior_probs, evidence):
        """
        贝叶斯概率更新
        
        Args:
            prior_probs: 先验概率
            evidence: 观测证据
        
        Returns:
            posterior_probs: 后验概率
        """
        # 计算似然函数
        likelihood = np.zeros_like(prior_probs)
        
        for i, prob in enumerate(prior_probs):
            # 基于历史频率计算似然
            ball_num = i + 1
            freq = self.base_predictor.red_ball_stats['frequency'].get(ball_num, 0)
            total_draws = len(self.data) * 6  # 总抽取次数
            likelihood[i] = freq / total_draws if total_draws > 0 else 1/33
        
        # 贝叶斯更新: P(H|E) = P(E|H) * P(H) / P(E)
        numerator = likelihood * prior_probs
        evidence_prob = numerator.sum()
        
        if evidence_prob > 0:
            posterior_probs = numerator / evidence_prob
        else:
            posterior_probs = prior_probs
        
        return posterior_probs
    
    def analyze_temporal_patterns(self, window_size=10):
        """
        时间序列模式分析
        
        Args:
            window_size: 滑动窗口大小
        
        Returns:
            patterns: 发现的时间模式
        """
        print(f"📈 分析时间序列模式（窗口大小：{window_size}）...")
        
        patterns = {
            'red_trends': [],
            'blue_trends': [],
            'cyclical_patterns': [],
            'seasonal_effects': []
        }
        
        # 分析红球趋势
        for i in range(window_size, len(self.data)):
            window_data = self.data.iloc[i-window_size:i]
            
            # 计算窗口内的统计特征
            red_means = []
            for _, row in window_data.iterrows():
                red_balls = [int(row[f'红球{j}']) for j in range(1, 7)]
                red_means.append(np.mean(red_balls))
            
            # 检测趋势
            if len(red_means) >= 3:
                trend = np.polyfit(range(len(red_means)), red_means, 1)[0]
                patterns['red_trends'].append(trend)
        
        # 分析蓝球趋势
        for i in range(window_size, len(self.data)):
            window_data = self.data.iloc[i-window_size:i]
            blue_values = window_data['蓝球'].values
            
            if len(blue_values) >= 3:
                trend = np.polyfit(range(len(blue_values)), blue_values, 1)[0]
                patterns['blue_trends'].append(trend)
        
        # 分析周期性模式
        red_sums = []
        for _, row in self.data.iterrows():
            red_balls = [int(row[f'红球{j}']) for j in range(1, 7)]
            red_sums.append(sum(red_balls))
        
        # 使用FFT检测周期性
        if len(red_sums) > 50:
            fft_result = np.fft.fft(red_sums[-100:])  # 分析最近100期
            frequencies = np.fft.fftfreq(100)
            dominant_freq = frequencies[np.argmax(np.abs(fft_result[1:]) + 1)]
            patterns['cyclical_patterns'].append(dominant_freq)
        
        return patterns
    
    def correlation_analysis(self, lag_periods=5):
        """
        相关性分析
        
        Args:
            lag_periods: 滞后期数
        
        Returns:
            correlations: 相关性分析结果
        """
        print(f"🔍 分析{lag_periods}期滞后相关性...")
        
        correlations = {
            'red_autocorr': [],
            'blue_autocorr': [],
            'cross_correlations': [],
            'pattern_correlations': []
        }
        
        # 准备时间序列数据
        red_series = []
        blue_series = []
        
        for _, row in self.data.iterrows():
            red_balls = [int(row[f'红球{j}']) for j in range(1, 7)]
            red_series.append(red_balls)
            blue_series.append(int(row['蓝球']))
        
        # 计算自相关
        for lag in range(1, lag_periods + 1):
            # 红球自相关（使用红球和作为代表）
            red_sums = [sum(balls) for balls in red_series]
            if len(red_sums) > lag:
                corr = np.corrcoef(red_sums[:-lag], red_sums[lag:])[0, 1]
                correlations['red_autocorr'].append((lag, corr))
            
            # 蓝球自相关
            if len(blue_series) > lag:
                corr = np.corrcoef(blue_series[:-lag], blue_series[lag:])[0, 1]
                correlations['blue_autocorr'].append((lag, corr))
        
        # 交叉相关分析
        red_sums = [sum(balls) for balls in red_series]
        for lag in range(1, lag_periods + 1):
            if len(red_sums) > lag and len(blue_series) > lag:
                # 红球和与蓝球的交叉相关
                corr = np.corrcoef(red_sums[:-lag], blue_series[lag:])[0, 1]
                correlations['cross_correlations'].append((lag, corr))
        
        return correlations
    
    def pattern_clustering(self, n_clusters=5):
        """
        模式聚类分析
        
        Args:
            n_clusters: 聚类数量
        
        Returns:
            cluster_info: 聚类信息
        """
        print(f"🎯 进行模式聚类分析（{n_clusters}个聚类）...")
        
        # 准备特征矩阵
        features = []
        for _, row in self.data.iterrows():
            red_balls = [int(row[f'红球{j}']) for j in range(1, 7)]
            blue_ball = int(row['蓝球'])
            
            # 构造特征向量
            feature_vector = [
                np.mean(red_balls),  # 红球平均值
                np.std(red_balls),   # 红球标准差
                max(red_balls) - min(red_balls),  # 红球跨度
                sum(1 for x in red_balls if x % 2 == 1),  # 奇数个数
                sum(1 for x in red_balls if x > 16),      # 大号个数
                blue_ball,  # 蓝球值
                sum(red_balls),  # 红球和
            ]
            features.append(feature_vector)
        
        features = np.array(features)
        
        # 标准化
        scaler = StandardScaler()
        features_scaled = scaler.fit_transform(features)
        
        # K-means聚类
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        cluster_labels = kmeans.fit_predict(features_scaled)
        
        # 分析每个聚类的特征
        cluster_info = {}
        for i in range(n_clusters):
            cluster_mask = cluster_labels == i
            cluster_data = self.data[cluster_mask]
            
            cluster_info[i] = {
                'size': cluster_mask.sum(),
                'percentage': cluster_mask.sum() / len(self.data) * 100,
                'center': kmeans.cluster_centers_[i],
                'recent_periods': cluster_data.tail(5)['期号'].tolist() if len(cluster_data) > 0 else []
            }
        
        self.pattern_clusters = {
            'kmeans': kmeans,
            'scaler': scaler,
            'labels': cluster_labels,
            'info': cluster_info
        }
        
        return cluster_info
    
    def predict_with_markov(self, period_index, order=1):
        """
        使用马尔可夫链预测
        
        Args:
            period_index: 当前期数索引
            order: 马尔可夫链阶数
        
        Returns:
            predicted_red, predicted_blue: 预测结果
        """
        if order not in self.transition_matrices:
            self.build_markov_chain(order)
        
        matrices = self.transition_matrices[order]
        
        # 获取前一期的号码作为当前状态
        if period_index >= order:
            prev_row = self.data.iloc[period_index - order]
            prev_red = [int(prev_row[f'红球{j}']) for j in range(1, 7)]
            prev_blue = int(prev_row['蓝球'])
            
            # 红球预测
            red_probs = np.zeros(33)
            for ball in prev_red:
                red_probs += matrices['red'][ball-1]
            red_probs = red_probs / len(prev_red)
            
            # 选择概率最高的6个红球（加入随机性）
            predicted_red = []
            available_balls = list(range(1, 34))
            
            for _ in range(6):
                if len(available_balls) == 0:
                    break
                
                # 计算可用球的概率
                available_probs = [red_probs[ball-1] for ball in available_balls]
                total_prob = sum(available_probs)
                
                if total_prob > 0:
                    available_probs = [p/total_prob for p in available_probs]
                    chosen_idx = np.random.choice(len(available_balls), p=available_probs)
                    chosen_ball = available_balls[chosen_idx]
                else:
                    chosen_ball = np.random.choice(available_balls)
                
                predicted_red.append(chosen_ball)
                available_balls.remove(chosen_ball)
            
            predicted_red = sorted(predicted_red)
            
            # 蓝球预测
            blue_probs = matrices['blue'][prev_blue-1]
            predicted_blue = np.random.choice(range(1, 17), p=blue_probs)
            
        else:
            # 数据不足时使用随机预测
            predicted_red = sorted(np.random.choice(range(1, 34), 6, replace=False))
            predicted_blue = np.random.choice(range(1, 17))
        
        return predicted_red, predicted_blue

    def predict_with_bayesian(self, period_index, evidence_window=20):
        """
        使用贝叶斯方法预测

        Args:
            period_index: 当前期数索引
            evidence_window: 证据窗口大小

        Returns:
            predicted_red, predicted_blue: 预测结果
        """
        # 设置先验概率（均匀分布）
        red_prior = np.ones(33) / 33
        blue_prior = np.ones(16) / 16

        # 收集最近的证据
        start_idx = max(0, period_index - evidence_window)
        recent_data = self.data.iloc[start_idx:period_index]

        # 更新红球概率
        red_evidence = np.zeros(33)
        for _, row in recent_data.iterrows():
            for j in range(1, 7):
                ball = int(row[f'红球{j}'])
                red_evidence[ball-1] += 1

        # 贝叶斯更新
        red_posterior = self.bayesian_update(red_prior, red_evidence)

        # 蓝球证据
        blue_evidence = np.zeros(16)
        for _, row in recent_data.iterrows():
            ball = int(row['蓝球'])
            blue_evidence[ball-1] += 1

        blue_posterior = self.bayesian_update(blue_prior, blue_evidence)

        # 基于后验概率选择号码
        predicted_red = []
        available_balls = list(range(1, 34))

        for _ in range(6):
            if len(available_balls) == 0:
                break

            available_probs = [red_posterior[ball-1] for ball in available_balls]
            total_prob = sum(available_probs)

            if total_prob > 0:
                available_probs = [p/total_prob for p in available_probs]
                chosen_idx = np.random.choice(len(available_balls), p=available_probs)
                chosen_ball = available_balls[chosen_idx]
            else:
                chosen_ball = np.random.choice(available_balls)

            predicted_red.append(chosen_ball)
            available_balls.remove(chosen_ball)

        predicted_red = sorted(predicted_red)
        predicted_blue = np.random.choice(range(1, 17), p=blue_posterior)

        return predicted_red, predicted_blue

    def predict_with_clustering(self, period_index):
        """
        基于聚类模式预测

        Args:
            period_index: 当前期数索引

        Returns:
            predicted_red, predicted_blue: 预测结果
        """
        if not self.pattern_clusters:
            self.pattern_clustering()

        # 获取最近几期的特征
        if period_index >= 3:
            recent_features = []
            for i in range(max(0, period_index-3), period_index):
                row = self.data.iloc[i]
                red_balls = [int(row[f'红球{j}']) for j in range(1, 7)]
                blue_ball = int(row['蓝球'])

                feature_vector = [
                    np.mean(red_balls),
                    np.std(red_balls),
                    max(red_balls) - min(red_balls),
                    sum(1 for x in red_balls if x % 2 == 1),
                    sum(1 for x in red_balls if x > 16),
                    blue_ball,
                    sum(red_balls),
                ]
                recent_features.append(feature_vector)

            # 预测当前期属于哪个聚类
            if recent_features:
                recent_features = np.array(recent_features)
                recent_features_scaled = self.pattern_clusters['scaler'].transform(recent_features)

                # 使用最近一期的特征预测聚类
                predicted_cluster = self.pattern_clusters['kmeans'].predict(recent_features_scaled[-1:])
                cluster_id = predicted_cluster[0]

                # 基于该聚类的历史数据生成预测
                cluster_mask = self.pattern_clusters['labels'] == cluster_id
                cluster_data = self.data[cluster_mask]

                if len(cluster_data) > 0:
                    # 分析该聚类的号码分布
                    red_freq = np.zeros(33)
                    blue_freq = np.zeros(16)

                    for _, row in cluster_data.iterrows():
                        for j in range(1, 7):
                            ball = int(row[f'红球{j}'])
                            red_freq[ball-1] += 1

                        ball = int(row['蓝球'])
                        blue_freq[ball-1] += 1

                    # 归一化为概率
                    red_probs = red_freq / red_freq.sum() if red_freq.sum() > 0 else np.ones(33) / 33
                    blue_probs = blue_freq / blue_freq.sum() if blue_freq.sum() > 0 else np.ones(16) / 16

                    # 选择号码
                    predicted_red = []
                    available_balls = list(range(1, 34))

                    for _ in range(6):
                        if len(available_balls) == 0:
                            break

                        available_probs = [red_probs[ball-1] for ball in available_balls]
                        total_prob = sum(available_probs)

                        if total_prob > 0:
                            available_probs = [p/total_prob for p in available_probs]
                            chosen_idx = np.random.choice(len(available_balls), p=available_probs)
                            chosen_ball = available_balls[chosen_idx]
                        else:
                            chosen_ball = np.random.choice(available_balls)

                        predicted_red.append(chosen_ball)
                        available_balls.remove(chosen_ball)

                    predicted_red = sorted(predicted_red)
                    predicted_blue = np.random.choice(range(1, 17), p=blue_probs)

                    return predicted_red, predicted_blue

        # 如果无法使用聚类，回退到随机预测
        predicted_red = sorted(np.random.choice(range(1, 34), 6, replace=False))
        predicted_blue = np.random.choice(range(1, 17))

        return predicted_red, predicted_blue

    def predict_with_time_series(self, period_index, lookback=30):
        """
        基于时间序列分析预测

        Args:
            period_index: 当前期数索引
            lookback: 回看期数

        Returns:
            predicted_red, predicted_blue: 预测结果
        """
        if period_index < lookback:
            lookback = period_index

        if lookback == 0:
            predicted_red = sorted(np.random.choice(range(1, 34), 6, replace=False))
            predicted_blue = np.random.choice(range(1, 17))
            return predicted_red, predicted_blue

        # 获取时间序列数据
        recent_data = self.data.iloc[period_index-lookback:period_index]

        # 分析红球趋势
        red_trends = []
        for ball_num in range(1, 34):
            appearances = []
            for i, (_, row) in enumerate(recent_data.iterrows()):
                red_balls = [int(row[f'红球{j}']) for j in range(1, 7)]
                appearances.append(1 if ball_num in red_balls else 0)

            # 计算趋势（线性回归斜率）
            if len(appearances) >= 3:
                x = np.arange(len(appearances))
                trend = np.polyfit(x, appearances, 1)[0]
                red_trends.append((ball_num, trend))

        # 根据趋势选择红球
        red_trends.sort(key=lambda x: x[1], reverse=True)  # 按趋势排序

        # 选择趋势最好的球，但加入随机性
        predicted_red = []
        trend_weights = np.array([max(0, trend) for _, trend in red_trends])

        if trend_weights.sum() > 0:
            trend_probs = trend_weights / trend_weights.sum()
            balls = [ball for ball, _ in red_trends]

            for _ in range(6):
                if len(balls) == 0:
                    break

                chosen_idx = np.random.choice(len(balls), p=trend_probs[:len(balls)])
                chosen_ball = balls[chosen_idx]
                predicted_red.append(chosen_ball)

                # 移除已选择的球
                balls.pop(chosen_idx)
                trend_probs = np.delete(trend_probs, chosen_idx)
                if trend_probs.sum() > 0:
                    trend_probs = trend_probs / trend_probs.sum()

        if len(predicted_red) < 6:
            # 补充随机球
            available = [i for i in range(1, 34) if i not in predicted_red]
            needed = 6 - len(predicted_red)
            predicted_red.extend(np.random.choice(available, needed, replace=False))

        predicted_red = sorted(predicted_red)

        # 蓝球时间序列分析
        blue_values = recent_data['蓝球'].values
        if len(blue_values) >= 3:
            # 简单的线性外推
            x = np.arange(len(blue_values))
            trend = np.polyfit(x, blue_values, 1)[0]
            next_blue = blue_values[-1] + trend
            predicted_blue = max(1, min(16, int(round(next_blue))))
        else:
            predicted_blue = np.random.choice(range(1, 17))

        return predicted_red, predicted_blue

    def ensemble_predict(self, period_index, methods=None):
        """
        集成预测方法

        Args:
            period_index: 当前期数索引
            methods: 要使用的方法列表

        Returns:
            predicted_red, predicted_blue: 集成预测结果
        """
        if methods is None:
            methods = ['markov', 'bayesian', 'clustering', 'time_series', 'statistical', 'ml']

        print(f"🎯 使用集成方法预测第{period_index}期...")

        all_predictions = {}

        # 收集各种方法的预测
        for method in methods:
            try:
                if method == 'markov':
                    red, blue = self.predict_with_markov(period_index, order=1)
                    all_predictions['markov_1'] = {'red': red, 'blue': blue}

                    red, blue = self.predict_with_markov(period_index, order=2)
                    all_predictions['markov_2'] = {'red': red, 'blue': blue}

                elif method == 'bayesian':
                    red, blue = self.predict_with_bayesian(period_index)
                    all_predictions['bayesian'] = {'red': red, 'blue': blue}

                elif method == 'clustering':
                    red, blue = self.predict_with_clustering(period_index)
                    all_predictions['clustering'] = {'red': red, 'blue': blue}

                elif method == 'time_series':
                    red, blue = self.predict_with_time_series(period_index)
                    all_predictions['time_series'] = {'red': red, 'blue': blue}

                elif method == 'statistical':
                    red, blue = self.base_predictor.predict_numbers_statistical(period_index)
                    all_predictions['statistical'] = {'red': red, 'blue': blue}

                elif method == 'ml':
                    red, blue = self.base_predictor.predict_numbers_ml(period_index)
                    all_predictions['ml'] = {'red': red, 'blue': blue}

            except Exception as e:
                print(f"⚠️ 方法 {method} 预测失败: {e}")
                continue

        if not all_predictions:
            # 如果所有方法都失败，使用随机预测
            predicted_red = sorted(np.random.choice(range(1, 34), 6, replace=False))
            predicted_blue = np.random.choice(range(1, 17))
            return predicted_red, predicted_blue

        # 集成红球预测（投票机制）
        red_votes = {}
        for method_name, pred in all_predictions.items():
            for ball in pred['red']:
                red_votes[ball] = red_votes.get(ball, 0) + 1

        # 选择得票最多的红球
        sorted_red_votes = sorted(red_votes.items(), key=lambda x: x[1], reverse=True)

        # 智能选择：结合得票数和随机性
        predicted_red = []
        available_balls = [ball for ball, votes in sorted_red_votes]
        vote_weights = [votes for ball, votes in sorted_red_votes]

        # 归一化权重
        total_votes = sum(vote_weights)
        if total_votes > 0:
            vote_probs = [w/total_votes for w in vote_weights]
        else:
            vote_probs = [1/len(available_balls)] * len(available_balls)

        # 加权随机选择
        for _ in range(6):
            if len(available_balls) == 0:
                break

            chosen_idx = np.random.choice(len(available_balls), p=vote_probs[:len(available_balls)])
            chosen_ball = available_balls[chosen_idx]
            predicted_red.append(chosen_ball)

            # 移除已选择的球
            available_balls.pop(chosen_idx)
            vote_probs = np.delete(vote_probs, chosen_idx)
            if len(vote_probs) > 0 and vote_probs.sum() > 0:
                vote_probs = vote_probs / vote_probs.sum()

        # 如果不足6个，随机补充
        if len(predicted_red) < 6:
            remaining = [i for i in range(1, 34) if i not in predicted_red]
            needed = 6 - len(predicted_red)
            predicted_red.extend(np.random.choice(remaining, needed, replace=False))

        predicted_red = sorted(predicted_red)

        # 集成蓝球预测（加权平均）
        blue_predictions = [pred['blue'] for pred in all_predictions.values()]
        predicted_blue = int(round(np.mean(blue_predictions)))
        predicted_blue = max(1, min(16, predicted_blue))

        return predicted_red, predicted_blue, all_predictions

    def analyze_method_performance(self, start_period=200, end_period=None, test_size=50):
        """
        分析各种方法的性能

        Args:
            start_period: 开始测试期
            end_period: 结束测试期
            test_size: 测试样本大小

        Returns:
            performance_results: 性能分析结果
        """
        if end_period is None:
            end_period = len(self.data) - 1

        # 选择测试期数
        test_periods = np.linspace(start_period, end_period, test_size, dtype=int)

        methods = ['markov', 'bayesian', 'clustering', 'time_series']
        performance = {method: {'red_hits': [], 'blue_hits': []} for method in methods}
        performance['ensemble'] = {'red_hits': [], 'blue_hits': []}

        print(f"🧪 测试{len(test_periods)}个期数的方法性能...")

        for i, period_idx in enumerate(test_periods):
            if i % 10 == 0:
                print(f"   进度: {i}/{len(test_periods)}")

            # 获取实际结果
            actual_row = self.data.iloc[period_idx]
            actual_red = [int(actual_row[f'红球{j}']) for j in range(1, 7)]
            actual_blue = int(actual_row['蓝球'])

            # 测试各种方法
            for method in methods:
                try:
                    if method == 'markov':
                        pred_red, pred_blue = self.predict_with_markov(period_idx)
                    elif method == 'bayesian':
                        pred_red, pred_blue = self.predict_with_bayesian(period_idx)
                    elif method == 'clustering':
                        pred_red, pred_blue = self.predict_with_clustering(period_idx)
                    elif method == 'time_series':
                        pred_red, pred_blue = self.predict_with_time_series(period_idx)

                    # 计算命中数
                    red_hits = len(set(pred_red) & set(actual_red))
                    blue_hit = 1 if pred_blue == actual_blue else 0

                    performance[method]['red_hits'].append(red_hits)
                    performance[method]['blue_hits'].append(blue_hit)

                except Exception as e:
                    performance[method]['red_hits'].append(0)
                    performance[method]['blue_hits'].append(0)

            # 测试集成方法
            try:
                pred_red, pred_blue, _ = self.ensemble_predict(period_idx)
                red_hits = len(set(pred_red) & set(actual_red))
                blue_hit = 1 if pred_blue == actual_blue else 0

                performance['ensemble']['red_hits'].append(red_hits)
                performance['ensemble']['blue_hits'].append(blue_hit)
            except Exception as e:
                performance['ensemble']['red_hits'].append(0)
                performance['ensemble']['blue_hits'].append(0)

        # 计算统计结果
        results = {}
        for method, data in performance.items():
            red_avg = np.mean(data['red_hits'])
            blue_rate = np.mean(data['blue_hits']) * 100

            results[method] = {
                'avg_red_hits': red_avg,
                'blue_hit_rate': blue_rate,
                'total_tests': len(data['red_hits'])
            }

        return results
